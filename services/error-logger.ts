import * as Sentry from '@sentry/browser';

import { config } from '../core/config';

export class ErrorLogger {
  static init() {
    if (config.SENTRY_DSN) {
      Sentry.init({
        dsn: config.SENTRY_DSN,
        environment: config.ENVIRONMENT,
        tracesSampleRate: 1.0,
      });
    }
  }

  static capture(error: Error, context?: Record<string, unknown>) {
    console.error('[Error]', error, context);

    if (config.SENTRY_DSN) {
      Sentry.withScope(scope => {
        if (context) {
          scope.setContext('additional', context);
        }
        Sentry.captureException(error);
      });
    }
  }

  static setUser(user: { id: string; email?: string }) {
    Sentry.setUser({
      id: user.id,
      email: user.email,
    });
  }
}
