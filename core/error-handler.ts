import { analytics } from './analytics';

export abstract class ErrorHandler {
  private nextHandler: ErrorHandler | null = null;

  setNext(handler: ErrorHandler): ErrorHandler {
    this.nextHandler = handler;
    return handler;
  }

  handle(error: Error, context: Record<string, unknown>): void {
    if (this.canHandle(error)) {
      this.processError(error, context);
      analytics.trackFeatureUsage(`error_handled:${this.constructor.name}`);
    } else if (this.nextHandler) {
      this.nextHandler.handle(error, context);
    } else {
      console.error('Unhandled error:', error);
    }
  }

  protected abstract canHandle(error: Error): boolean;
  protected abstract processError(error: Error, context: Record<string, unknown>): void;
}

export class NetworkErrorHandler extends ErrorHandler {
  protected canHandle(error: Error): boolean {
    return error.name === 'NetworkError';
  }

  protected processError(error: Error): void {
    console.warn('Handling network error:', error.message);
  }
}

export class ValidationErrorHandler extends ErrorHandler {
  protected canHandle(error: Error): boolean {
    return error.name === 'ValidationError';
  }

  protected processError(error: Error): void {
    console.warn('Handling validation error:', error.message);
  }
}

export class CriticalErrorHandler extends ErrorHandler {
  protected canHandle(error: Error): boolean {
    return error.name === 'CriticalSystemError';
  }

  protected processError(error: Error): void {
    console.error('CRITICAL ERROR:', error);
    // Дополнительная логика обработки критических ошибок
  }
}

export const errorHandlerChain = new NetworkErrorHandler()
  .setNext(new ValidationErrorHandler())
  .setNext(new CriticalErrorHandler());
