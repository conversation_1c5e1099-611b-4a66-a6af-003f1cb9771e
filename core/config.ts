import { join } from 'path';
import { z } from 'zod';

// Environment schema for validation
const envSchema = z.object({
  API_URL: z.string().url(),
  DEBUG_MODE: z.enum(['true', 'false']).transform(val => val === 'true'),
  SENTRY_DSN: z.string().optional(),
  ENVIRONMENT: z.enum(['development', 'production', 'staging']),
});

export type EnvConfig = z.infer<typeof envSchema>;

// Validate environment variables
export const envConfig = envSchema.parse({
  API_URL: process.env.VITE_API_URL || 'http://localhost:3000',
  DEBUG_MODE: process.env.VITE_DEBUG_MODE || 'false',
  SENTRY_DSN: process.env.VITE_SENTRY_DSN,
  ENVIRONMENT: process.env.NODE_ENV || 'development',
});

// Main application configuration schema
export const AppConfigSchema = z.object({
  app: z.object({
    name: z.string(),
    version: z.string(),
    environment: z.enum(['development', 'staging', 'production']),
    debug: z.boolean(),
    logLevel: z.enum(['error', 'warn', 'info', 'debug', 'trace']),
  }),
  api: z.object({
    baseUrl: z.string().url(),
    timeout: z.number(),
    retryAttempts: z.number(),
    retryDelay: z.number(),
  }),
  security: z.object({
    encryption: z.object({
      algorithm: z.string(),
      keySize: z.number(),
      iterations: z.number(),
    }),
    jwt: z.object({
      secret: z.string(),
      expiresIn: z.string(),
      refreshExpiresIn: z.string(),
    }),
    sandbox: z.boolean(),
    contextIsolation: z.boolean(),
    nodeIntegration: z.boolean(),
    webSecurity: z.boolean(),
    allowRunningInsecureContent: z.boolean(),
    enableRemoteModule: z.boolean(),
  }),
  performance: z.object({
    cache: z.object({
      enabled: z.boolean(),
      ttl: z.number(),
      maxSize: z.number(),
    }),
    compression: z.object({
      enabled: z.boolean(),
      level: z.number(),
    }),
    maxMemory: z.number(),
    maxCpu: z.number(),
    maxTabs: z.number(),
    tabSleepTimeout: z.number(),
    cacheSize: z.number(),
  }),
  monitoring: z.object({
    enabled: z.boolean(),
    metrics: z.object({
      enabled: z.boolean(),
      interval: z.number(),
    }),
    tracing: z.object({
      enabled: z.boolean(),
      sampling: z.number(),
    }),
  }),
  analytics: z.object({
    enabled: z.boolean(),
    providers: z.array(z.string()),
    events: z.array(z.string()),
  }),
  accessibility: z.object({
    enabled: z.boolean(),
    features: z.array(z.string()),
    compliance: z.array(z.string()),
  }),
  window: z.object({
    defaultWidth: z.number(),
    defaultHeight: z.number(),
    minWidth: z.number(),
    minHeight: z.number(),
    title: z.string(),
    backgroundColor: z.string(),
    showDevTools: z.boolean(),
  }),
  extensions: z.object({
    enabled: z.boolean(),
    autoUpdate: z.boolean(),
    sandbox: z.boolean(),
    maxMemory: z.number(),
    maxCpu: z.number(),
  }),
  privacy: z.object({
    doNotTrack: z.boolean(),
    blockAds: z.boolean(),
    blockTrackers: z.boolean(),
    blockFingerprinting: z.boolean(),
    blockWebRTC: z.boolean(),
    clearOnExit: z.boolean(),
  }),
  sync: z.object({
    enabled: z.boolean(),
    interval: z.number(),
    maxRetries: z.number(),
    timeout: z.number(),
  }),
  updates: z.object({
    autoUpdate: z.boolean(),
    checkInterval: z.number(),
    channel: z.enum(['stable', 'beta', 'nightly']),
  }),
  logging: z.object({
    level: z.enum(['error', 'warn', 'info', 'debug']),
    maxSize: z.number(),
    maxFiles: z.number(),
  }),
  paths: z.object({
    root: z.string(),
    src: z.string(),
    dist: z.string(),
    assets: z.string(),
    locales: z.string(),
    extensions: z.string(),
    userData: z.string(),
    logs: z.string(),
    temp: z.string(),
  }),
  errorReporting: z.object({
    enabled: z.boolean(),
    dsn: z.string().optional(),
    environment: z.string(),
  }),
});

export type AppConfig = z.infer<typeof AppConfigSchema>;

const isDev = envConfig.ENVIRONMENT === 'development';
const isProd = envConfig.ENVIRONMENT === 'production';
const isTest = envConfig.ENVIRONMENT === 'staging';

// Default configuration
export const defaultConfig: AppConfig = {
  app: {
    name: 'A14-Browser',
    version: process.env.npm_package_version || '1.0.0',
    environment: envConfig.ENVIRONMENT,
    debug: envConfig.DEBUG_MODE,
    logLevel: isDev ? 'debug' : 'info',
  },
  api: {
    baseUrl: envConfig.API_URL,
    timeout: 5000,
    retryAttempts: 3,
    retryDelay: 1000,
  },
  security: {
    encryption: {
      algorithm: 'aes-256-gcm',
      keySize: 32,
      iterations: 100000,
    },
    jwt: {
      secret: process.env.JWT_SECRET || 'your-secret-key',
      expiresIn: '1h',
      refreshExpiresIn: '7d',
    },
    sandbox: true,
    contextIsolation: true,
    nodeIntegration: false,
    webSecurity: true,
    allowRunningInsecureContent: false,
    enableRemoteModule: false,
  },
  performance: {
    cache: {
      enabled: true,
      ttl: 3600,
      maxSize: 1000,
    },
    compression: {
      enabled: true,
      level: 6,
    },
    maxMemory: 2048, // MB
    maxCpu: 80, // percentage
    maxTabs: 100,
    tabSleepTimeout: 300000, // 5 minutes
    cacheSize: 1024, // MB
  },
  monitoring: {
    enabled: true,
    metrics: {
      enabled: true,
      interval: 60000,
    },
    tracing: {
      enabled: true,
      sampling: 0.1,
    },
  },
  analytics: {
    enabled: true,
    providers: ['google-analytics', 'mixpanel'],
    events: ['page_view', 'user_action', 'error'],
  },
  accessibility: {
    enabled: true,
    features: ['screen-reader', 'keyboard-navigation', 'high-contrast'],
    compliance: ['WCAG2.1', 'ADA'],
  },
  window: {
    defaultWidth: 1280,
    defaultHeight: 800,
    minWidth: 800,
    minHeight: 600,
    title: 'A14 Browser',
    backgroundColor: '#ffffff',
    showDevTools: isDev,
  },
  extensions: {
    enabled: true,
    autoUpdate: true,
    sandbox: true,
    maxMemory: 512, // MB
    maxCpu: 50, // percentage
  },
  privacy: {
    doNotTrack: true,
    blockAds: true,
    blockTrackers: true,
    blockFingerprinting: true,
    blockWebRTC: true,
    clearOnExit: false,
  },
  sync: {
    enabled: true,
    interval: 300000, // 5 minutes
    maxRetries: 3,
    timeout: 30000, // 30 seconds
  },
  updates: {
    autoUpdate: true,
    checkInterval: 3600000, // 1 hour
    channel: 'stable',
  },
  logging: {
    level: isDev ? 'debug' : 'info',
    maxSize: 10, // MB
    maxFiles: 5,
  },
  paths: {
    root: process.cwd(),
    src: join(process.cwd(), 'src'),
    dist: join(process.cwd(), 'dist'),
    assets: join(process.cwd(), 'src/assets'),
    locales: join(process.cwd(), 'src/i18n/locales'),
    extensions: join(process.cwd(), 'extensions'),
    userData: join(process.cwd(), 'userData'),
    logs: join(process.cwd(), 'logs'),
    temp: join(process.cwd(), 'temp'),
  },
  errorReporting: {
    enabled: !!envConfig.SENTRY_DSN,
    dsn: envConfig.SENTRY_DSN,
    environment: envConfig.ENVIRONMENT,
  },
};

// Validate and export the configuration
export const config = AppConfigSchema.parse(defaultConfig);

// Export legacy interface for backward compatibility
export const APP_CONFIG = config;
