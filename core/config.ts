import { z } from 'zod';

const envSchema = z.object({
  API_URL: z.string().url(),
  DEBUG_MODE: z.enum(['true', 'false']).transform(val => val === 'true'),
  SENTRY_DSN: z.string().optional(),
  ENVIRONMENT: z.enum(['development', 'production', 'staging']),
});

export type AppConfig = z.infer<typeof envSchema>;

export const config = envSchema.parse({
  API_URL: import.meta.env.VITE_API_URL,
  DEBUG_MODE: import.meta.env.VITE_DEBUG_MODE,
  SENTRY_DSN: import.meta.env.VITE_SENTRY_DSN,
  ENVIRONMENT: import.meta.env.MODE,
});
