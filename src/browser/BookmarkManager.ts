import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';
import { cryptographicService } from '../security/CryptographicService';

export interface Bookmark {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  description?: string;
  tags: string[];
  folderId?: string;
  createdAt: number;
  updatedAt: number;
  lastVisited?: number;
  visitCount: number;
  isPrivate: boolean;
  metadata: BookmarkMetadata;
}

export interface BookmarkMetadata {
  pageTitle?: string;
  pageDescription?: string;
  keywords?: string[];
  thumbnail?: string;
  contentType?: string;
  language?: string;
  author?: string;
  publishedDate?: number;
}

export interface BookmarkFolder {
  id: string;
  name: string;
  parentId?: string;
  children: string[];
  createdAt: number;
  updatedAt: number;
  isPrivate: boolean;
  color?: string;
  icon?: string;
}

export interface BookmarkImportResult {
  imported: number;
  skipped: number;
  errors: number;
  duplicates: number;
  details: Array<{
    url: string;
    status: 'imported' | 'skipped' | 'error' | 'duplicate';
    reason?: string;
  }>;
}

export interface BookmarkSearchOptions {
  query?: string;
  tags?: string[];
  folderId?: string;
  dateRange?: {
    start: number;
    end: number;
  };
  sortBy?: 'title' | 'url' | 'createdAt' | 'lastVisited' | 'visitCount';
  sortOrder?: 'asc' | 'desc';
  limit?: number;
  includePrivate?: boolean;
}

export class BookmarkManager extends EventEmitter {
  private static instance: BookmarkManager;
  private bookmarks: Map<string, Bookmark> = new Map();
  private folders: Map<string, BookmarkFolder> = new Map();
  private urlIndex: Map<string, string> = new Map(); // URL -> bookmark ID
  private tagIndex: Map<string, Set<string>> = new Map(); // tag -> bookmark IDs
  private searchIndex: Map<string, Set<string>> = new Map(); // word -> bookmark IDs

  private constructor() {
    super();
    this.initializeBookmarkManager();
  }

  public static getInstance(): BookmarkManager {
    if (!BookmarkManager.instance) {
      BookmarkManager.instance = new BookmarkManager();
    }
    return BookmarkManager.instance;
  }

  private async initializeBookmarkManager(): Promise<void> {
    // Create default folders
    await this.createDefaultFolders();

    // Load bookmarks from storage
    await this.loadBookmarks();

    // Build search indices
    this.buildSearchIndices();

    logger.info('Bookmark manager initialized', {
      bookmarkCount: this.bookmarks.size,
      folderCount: this.folders.size,
    });
  }

  private async createDefaultFolders(): Promise<void> {
    const defaultFolders = [
      { name: 'Bookmarks Bar', id: 'bookmarks_bar' },
      { name: 'Other Bookmarks', id: 'other_bookmarks' },
      { name: 'Mobile Bookmarks', id: 'mobile_bookmarks' },
    ];

    for (const folderData of defaultFolders) {
      const folder: BookmarkFolder = {
        id: folderData.id,
        name: folderData.name,
        children: [],
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isPrivate: false,
      };

      this.folders.set(folder.id, folder);
    }
  }

  public async addBookmark(bookmarkData: {
    title: string;
    url: string;
    folderId?: string;
    tags?: string[];
    description?: string;
    isPrivate?: boolean;
  }): Promise<Bookmark> {
    // Check for duplicates
    const existingId = this.urlIndex.get(bookmarkData.url);
    if (existingId) {
      const existing = this.bookmarks.get(existingId);
      if (existing) {
        throw new Error('Bookmark already exists');
      }
    }

    const bookmarkId = `bookmark_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // Fetch metadata
    const metadata = await this.fetchBookmarkMetadata(bookmarkData.url);

    const bookmark: Bookmark = {
      id: bookmarkId,
      title: bookmarkData.title,
      url: bookmarkData.url,
      description: bookmarkData.description,
      tags: bookmarkData.tags || [],
      folderId: bookmarkData.folderId || 'other_bookmarks',
      createdAt: Date.now(),
      updatedAt: Date.now(),
      visitCount: 0,
      isPrivate: bookmarkData.isPrivate || false,
      metadata,
    };

    // Add to collections
    this.bookmarks.set(bookmarkId, bookmark);
    this.urlIndex.set(bookmark.url, bookmarkId);

    // Add to folder
    if (bookmark.folderId) {
      const folder = this.folders.get(bookmark.folderId);
      if (folder) {
        folder.children.push(bookmarkId);
        folder.updatedAt = Date.now();
      }
    }

    // Update indices
    this.updateSearchIndex(bookmark);
    this.updateTagIndex(bookmark);

    // Save to storage
    await this.saveBookmarks();

    this.emit('bookmark_added', bookmark);
    logger.info('Bookmark added', {
      bookmarkId,
      title: bookmark.title,
      url: bookmark.url,
      folderId: bookmark.folderId,
    });

    return bookmark;
  }

  public async updateBookmark(bookmarkId: string, updates: Partial<Bookmark>): Promise<Bookmark> {
    const bookmark = this.bookmarks.get(bookmarkId);
    if (!bookmark) {
      throw new Error(`Bookmark ${bookmarkId} not found`);
    }

    // Remove from old indices
    this.removeFromSearchIndex(bookmark);
    this.removeFromTagIndex(bookmark);

    // Update URL index if URL changed
    if (updates.url && updates.url !== bookmark.url) {
      this.urlIndex.delete(bookmark.url);
      this.urlIndex.set(updates.url, bookmarkId);
    }

    // Apply updates
    const updatedBookmark = {
      ...bookmark,
      ...updates,
      id: bookmarkId, // Ensure ID doesn't change
      updatedAt: Date.now(),
    };

    this.bookmarks.set(bookmarkId, updatedBookmark);

    // Update indices
    this.updateSearchIndex(updatedBookmark);
    this.updateTagIndex(updatedBookmark);

    // Save to storage
    await this.saveBookmarks();

    this.emit('bookmark_updated', updatedBookmark);
    logger.info('Bookmark updated', { bookmarkId, updates });

    return updatedBookmark;
  }

  public async deleteBookmark(bookmarkId: string): Promise<void> {
    const bookmark = this.bookmarks.get(bookmarkId);
    if (!bookmark) {
      throw new Error(`Bookmark ${bookmarkId} not found`);
    }

    // Remove from collections
    this.bookmarks.delete(bookmarkId);
    this.urlIndex.delete(bookmark.url);

    // Remove from folder
    if (bookmark.folderId) {
      const folder = this.folders.get(bookmark.folderId);
      if (folder) {
        const index = folder.children.indexOf(bookmarkId);
        if (index > -1) {
          folder.children.splice(index, 1);
          folder.updatedAt = Date.now();
        }
      }
    }

    // Remove from indices
    this.removeFromSearchIndex(bookmark);
    this.removeFromTagIndex(bookmark);

    // Save to storage
    await this.saveBookmarks();

    this.emit('bookmark_deleted', { bookmarkId, bookmark });
    logger.info('Bookmark deleted', { bookmarkId, url: bookmark.url });
  }

  public async createFolder(folderData: {
    name: string;
    parentId?: string;
    isPrivate?: boolean;
    color?: string;
    icon?: string;
  }): Promise<BookmarkFolder> {
    const folderId = `folder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const folder: BookmarkFolder = {
      id: folderId,
      name: folderData.name,
      parentId: folderData.parentId,
      children: [],
      createdAt: Date.now(),
      updatedAt: Date.now(),
      isPrivate: folderData.isPrivate || false,
      color: folderData.color,
      icon: folderData.icon,
    };

    this.folders.set(folderId, folder);

    // Add to parent folder
    if (folder.parentId) {
      const parentFolder = this.folders.get(folder.parentId);
      if (parentFolder) {
        parentFolder.children.push(folderId);
        parentFolder.updatedAt = Date.now();
      }
    }

    // Save to storage
    await this.saveBookmarks();

    this.emit('folder_created', folder);
    logger.info('Bookmark folder created', { folderId, name: folder.name });

    return folder;
  }

  public async deleteFolder(folderId: string, moveBookmarksTo?: string): Promise<void> {
    const folder = this.folders.get(folderId);
    if (!folder) {
      throw new Error(`Folder ${folderId} not found`);
    }

    // Handle bookmarks in folder
    const bookmarksInFolder = Array.from(this.bookmarks.values()).filter(
      bookmark => bookmark.folderId === folderId
    );

    for (const bookmark of bookmarksInFolder) {
      if (moveBookmarksTo) {
        await this.updateBookmark(bookmark.id, { folderId: moveBookmarksTo });
      } else {
        await this.deleteBookmark(bookmark.id);
      }
    }

    // Handle subfolders
    const subfolders = Array.from(this.folders.values()).filter(f => f.parentId === folderId);

    for (const subfolder of subfolders) {
      await this.deleteFolder(subfolder.id, moveBookmarksTo);
    }

    // Remove from parent folder
    if (folder.parentId) {
      const parentFolder = this.folders.get(folder.parentId);
      if (parentFolder) {
        const index = parentFolder.children.indexOf(folderId);
        if (index > -1) {
          parentFolder.children.splice(index, 1);
          parentFolder.updatedAt = Date.now();
        }
      }
    }

    this.folders.delete(folderId);

    // Save to storage
    await this.saveBookmarks();

    this.emit('folder_deleted', { folderId, folder });
    logger.info('Bookmark folder deleted', { folderId, name: folder.name });
  }

  public searchBookmarks(options: BookmarkSearchOptions): Bookmark[] {
    let results = Array.from(this.bookmarks.values());

    // Filter by privacy
    if (!options.includePrivate) {
      results = results.filter(bookmark => !bookmark.isPrivate);
    }

    // Filter by folder
    if (options.folderId) {
      results = results.filter(bookmark => bookmark.folderId === options.folderId);
    }

    // Filter by tags
    if (options.tags && options.tags.length > 0) {
      results = results.filter(bookmark => options.tags!.every(tag => bookmark.tags.includes(tag)));
    }

    // Filter by date range
    if (options.dateRange) {
      results = results.filter(
        bookmark =>
          bookmark.createdAt >= options.dateRange!.start &&
          bookmark.createdAt <= options.dateRange!.end
      );
    }

    // Text search
    if (options.query) {
      const query = options.query.toLowerCase();
      results = results.filter(
        bookmark =>
          bookmark.title.toLowerCase().includes(query) ||
          bookmark.url.toLowerCase().includes(query) ||
          (bookmark.description && bookmark.description.toLowerCase().includes(query)) ||
          bookmark.tags.some(tag => tag.toLowerCase().includes(query))
      );
    }

    // Sort results
    if (options.sortBy) {
      results.sort((a, b) => {
        let aValue: any, bValue: any;

        switch (options.sortBy) {
          case 'title':
            aValue = a.title.toLowerCase();
            bValue = b.title.toLowerCase();
            break;
          case 'url':
            aValue = a.url.toLowerCase();
            bValue = b.url.toLowerCase();
            break;
          case 'createdAt':
            aValue = a.createdAt;
            bValue = b.createdAt;
            break;
          case 'lastVisited':
            aValue = a.lastVisited || 0;
            bValue = b.lastVisited || 0;
            break;
          case 'visitCount':
            aValue = a.visitCount;
            bValue = b.visitCount;
            break;
          default:
            return 0;
        }

        if (aValue < bValue) return options.sortOrder === 'desc' ? 1 : -1;
        if (aValue > bValue) return options.sortOrder === 'desc' ? -1 : 1;
        return 0;
      });
    }

    // Limit results
    if (options.limit) {
      results = results.slice(0, options.limit);
    }

    return results;
  }

  public async importBookmarks(
    data: string,
    format: 'html' | 'json' | 'csv'
  ): Promise<BookmarkImportResult> {
    const result: BookmarkImportResult = {
      imported: 0,
      skipped: 0,
      errors: 0,
      duplicates: 0,
      details: [],
    };

    try {
      let bookmarksToImport: Array<{
        title: string;
        url: string;
        description?: string;
        tags?: string[];
        folderId?: string;
      }> = [];

      switch (format) {
        case 'html':
          bookmarksToImport = this.parseHTMLBookmarks(data);
          break;
        case 'json':
          bookmarksToImport = JSON.parse(data);
          break;
        case 'csv':
          bookmarksToImport = this.parseCSVBookmarks(data);
          break;
      }

      for (const bookmarkData of bookmarksToImport) {
        try {
          // Check for duplicates
          if (this.urlIndex.has(bookmarkData.url)) {
            result.duplicates++;
            result.details.push({
              url: bookmarkData.url,
              status: 'duplicate',
              reason: 'URL already exists',
            });
            continue;
          }

          await this.addBookmark(bookmarkData);
          result.imported++;
          result.details.push({
            url: bookmarkData.url,
            status: 'imported',
          });
        } catch (error) {
          result.errors++;
          result.details.push({
            url: bookmarkData.url,
            status: 'error',
            reason: error instanceof Error ? error.message : String(error),
          });
        }
      }

      this.emit('bookmarks_imported', result);
      logger.info('Bookmarks imported', result);

      return result;
    } catch (error) {
      logger.error('Failed to import bookmarks', error);
      throw error;
    }
  }

  public async exportBookmarks(
    format: 'html' | 'json' | 'csv',
    includePrivate: boolean = false
  ): Promise<string> {
    const bookmarks = Array.from(this.bookmarks.values()).filter(
      bookmark => includePrivate || !bookmark.isPrivate
    );

    switch (format) {
      case 'html':
        return this.exportToHTML(bookmarks);
      case 'json':
        return JSON.stringify(bookmarks, null, 2);
      case 'csv':
        return this.exportToCSV(bookmarks);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  private parseHTMLBookmarks(
    html: string
  ): Array<{ title: string; url: string; description?: string }> {
    // Simple HTML bookmark parser
    const bookmarks = [];
    const linkRegex = /<a[^>]+href="([^"]+)"[^>]*>([^<]+)<\/a>/gi;
    let match;

    while ((match = linkRegex.exec(html)) !== null) {
      bookmarks.push({
        title: match[2].trim(),
        url: match[1],
      });
    }

    return bookmarks;
  }

  private parseCSVBookmarks(
    csv: string
  ): Array<{ title: string; url: string; description?: string }> {
    const lines = csv.split('\n');
    const bookmarks = [];

    for (let i = 1; i < lines.length; i++) {
      // Skip header
      const columns = lines[i].split(',');
      if (columns.length >= 2) {
        bookmarks.push({
          title: columns[0].trim(),
          url: columns[1].trim(),
          description: columns[2]?.trim(),
        });
      }
    }

    return bookmarks;
  }

  private exportToHTML(bookmarks: Bookmark[]): string {
    let html =
      '<!DOCTYPE NETSCAPE-Bookmark-file-1>\n<HTML>\n<HEAD>\n<TITLE>Bookmarks</TITLE>\n</HEAD>\n<BODY>\n<H1>Bookmarks</H1>\n<DL>\n';

    for (const bookmark of bookmarks) {
      html += `<DT><A HREF="${bookmark.url}">${bookmark.title}</A>\n`;
      if (bookmark.description) {
        html += `<DD>${bookmark.description}\n`;
      }
    }

    html += '</DL>\n</BODY>\n</HTML>';
    return html;
  }

  private exportToCSV(bookmarks: Bookmark[]): string {
    let csv = 'Title,URL,Description,Tags\n';

    for (const bookmark of bookmarks) {
      const title = bookmark.title.replace(/"/g, '""');
      const description = (bookmark.description || '').replace(/"/g, '""');
      const tags = bookmark.tags.join(';');
      csv += `"${title}","${bookmark.url}","${description}","${tags}"\n`;
    }

    return csv;
  }

  private async fetchBookmarkMetadata(url: string): Promise<BookmarkMetadata> {
    try {
      // In a real implementation, this would fetch the page and extract metadata
      return {
        pageTitle: 'Page Title',
        pageDescription: 'Page description',
        keywords: [],
        contentType: 'text/html',
        language: 'en',
      };
    } catch (error) {
      logger.warn('Failed to fetch bookmark metadata', { url, error });
      return {};
    }
  }

  private buildSearchIndices(): void {
    this.searchIndex.clear();
    this.tagIndex.clear();

    for (const bookmark of this.bookmarks.values()) {
      this.updateSearchIndex(bookmark);
      this.updateTagIndex(bookmark);
    }
  }

  private updateSearchIndex(bookmark: Bookmark): void {
    const words = [
      ...bookmark.title.toLowerCase().split(/\s+/),
      ...bookmark.url.toLowerCase().split(/[\/\.\-_]/),
      ...(bookmark.description || '').toLowerCase().split(/\s+/),
      ...bookmark.tags.map(tag => tag.toLowerCase()),
    ];

    for (const word of words) {
      if (word.length > 2) {
        if (!this.searchIndex.has(word)) {
          this.searchIndex.set(word, new Set());
        }
        this.searchIndex.get(word)!.add(bookmark.id);
      }
    }
  }

  private removeFromSearchIndex(bookmark: Bookmark): void {
    for (const wordSet of this.searchIndex.values()) {
      wordSet.delete(bookmark.id);
    }
  }

  private updateTagIndex(bookmark: Bookmark): void {
    for (const tag of bookmark.tags) {
      if (!this.tagIndex.has(tag)) {
        this.tagIndex.set(tag, new Set());
      }
      this.tagIndex.get(tag)!.add(bookmark.id);
    }
  }

  private removeFromTagIndex(bookmark: Bookmark): void {
    for (const tag of bookmark.tags) {
      const tagSet = this.tagIndex.get(tag);
      if (tagSet) {
        tagSet.delete(bookmark.id);
        if (tagSet.size === 0) {
          this.tagIndex.delete(tag);
        }
      }
    }
  }

  private async loadBookmarks(): Promise<void> {
    try {
      // In a real implementation, load from persistent storage
      logger.debug('Bookmarks loaded from storage');
    } catch (error) {
      logger.warn('Failed to load bookmarks', { error });
    }
  }

  private async saveBookmarks(): Promise<void> {
    try {
      // In a real implementation, save to persistent storage
      logger.debug('Bookmarks saved to storage');
    } catch (error) {
      logger.warn('Failed to save bookmarks', { error });
    }
  }

  // Getters
  public getBookmarks(): Bookmark[] {
    return Array.from(this.bookmarks.values());
  }

  public getBookmark(bookmarkId: string): Bookmark | null {
    return this.bookmarks.get(bookmarkId) || null;
  }

  public getFolders(): BookmarkFolder[] {
    return Array.from(this.folders.values());
  }

  public getFolder(folderId: string): BookmarkFolder | null {
    return this.folders.get(folderId) || null;
  }

  public getAllTags(): string[] {
    return Array.from(this.tagIndex.keys());
  }

  public getBookmarkCount(): number {
    return this.bookmarks.size;
  }

  public getFolderCount(): number {
    return this.folders.size;
  }
}

// Export singleton instance
export const bookmarkManager = BookmarkManager.getInstance();
