import React, { createContext, useContext, useEffect, useState } from 'react';

import { useStorage } from './StorageProvider';

type ThemeMode = 'light' | 'dark' | 'system';

interface ThemeColors {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

interface Theme {
  mode: ThemeMode;
  colors: ThemeColors;
  fontFamily: string;
  fontSize: {
    small: string;
    medium: string;
    large: string;
  };
  spacing: {
    small: string;
    medium: string;
    large: string;
  };
  borderRadius: string;
  transition: string;
}

interface ThemeContextType {
  theme: Theme;
  setThemeMode: (mode: ThemeMode) => Promise<void>;
  updateThemeColors: (colors: Partial<ThemeColors>) => Promise<void>;
  updateThemeFont: (fontFamily: string) => Promise<void>;
  updateThemeFontSize: (sizes: Partial<Theme['fontSize']>) => Promise<void>;
  updateThemeSpacing: (spacing: Partial<Theme['spacing']>) => Promise<void>;
  resetTheme: () => Promise<void>;
}

const defaultTheme: Theme = {
  mode: 'system',
  colors: {
    primary: '#2196f3',
    secondary: '#f50057',
    background: '#ffffff',
    surface: '#f5f5f5',
    text: '#000000',
    textSecondary: '#666666',
    border: '#e0e0e0',
    error: '#f44336',
    warning: '#ff9800',
    success: '#4caf50',
    info: '#2196f3',
  },
  fontFamily: 'system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
  fontSize: {
    small: '0.875rem',
    medium: '1rem',
    large: '1.25rem',
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '2rem',
  },
  borderRadius: '4px',
  transition: 'all 0.3s ease',
};

const ThemeContext = createContext<ThemeContextType | null>(null);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const storage = useStorage();
  const [theme, setTheme] = useState<Theme>(defaultTheme);

  useEffect(() => {
    const loadTheme = async () => {
      try {
        const savedTheme = await storage.getItem('theme');
        if (savedTheme) {
          setTheme(JSON.parse(savedTheme));
        }
      } catch (error) {
        console.error('Error loading theme:', error);
      }
    };

    loadTheme();
  }, [storage]);

  const saveTheme = async (newTheme: Theme) => {
    try {
      await storage.setItem('theme', JSON.stringify(newTheme));
      setTheme(newTheme);
    } catch (error) {
      console.error('Error saving theme:', error);
    }
  };

  const setThemeMode = async (mode: ThemeMode) => {
    const newTheme = { ...theme, mode };
    await saveTheme(newTheme);
  };

  const updateThemeColors = async (colors: Partial<ThemeColors>) => {
    const newTheme = {
      ...theme,
      colors: { ...theme.colors, ...colors },
    };
    await saveTheme(newTheme);
  };

  const updateThemeFont = async (fontFamily: string) => {
    const newTheme = { ...theme, fontFamily };
    await saveTheme(newTheme);
  };

  const updateThemeFontSize = async (sizes: Partial<Theme['fontSize']>) => {
    const newTheme = {
      ...theme,
      fontSize: { ...theme.fontSize, ...sizes },
    };
    await saveTheme(newTheme);
  };

  const updateThemeSpacing = async (spacing: Partial<Theme['spacing']>) => {
    const newTheme = {
      ...theme,
      spacing: { ...theme.spacing, ...spacing },
    };
    await saveTheme(newTheme);
  };

  const resetTheme = async () => {
    await saveTheme(defaultTheme);
  };

  const value = {
    theme,
    setThemeMode,
    updateThemeColors,
    updateThemeFont,
    updateThemeFontSize,
    updateThemeSpacing,
    resetTheme,
  };

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>;
};
