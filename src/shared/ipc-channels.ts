/**
 * A single source of truth for all IPC channels.
 * This prevents typos and ensures consistency
 * between the main, renderer, and preload processes.
 */
export const IpcChannels = {
  GET_ALL_SETTINGS: 'settings:getAll',
  SET_SETTING: 'settings:set',

  GET_ALL_BOOKMARKS: 'bookmarks:getAll',
  ADD_BOOKMARK: 'bookmarks:add',
  REMOVE_BOOKMARK: 'bookmarks:remove',

  // Channels for events, not invocations
  UPDATE_AVAILABLE: 'update:available',
  UPDATE_DOWNLOADED: 'update:downloaded',
  QUIT_AND_INSTALL_UPDATE: 'update:quitAndInstall',
} as const;