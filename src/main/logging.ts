import { WriteStream, createWriteStream } from 'fs';
import { join } from 'path';
import { format } from 'util';

import { app } from 'electron';

interface LogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  data?: any;
  error?: Error;
  context?: Record<string, any>;
}

type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

class Logger {
  private logFile: string;
  private stream: WriteStream;
  private readonly maxFileSize: number = 5 * 1024 * 1024; // 5MB
  private readonly maxFiles: number = 5;
  private currentFileSize: number = 0;

  constructor() {
    this.logFile = join(app.getPath('userData'), 'app.log');
    this.stream = createWriteStream(this.logFile, { flags: 'a' });
    this.currentFileSize = this.getFileSize();
  }

  private getFileSize(): number {
    try {
      const stats = require('fs').statSync(this.logFile);
      return stats.size;
    } catch {
      return 0;
    }
  }

  private rotateLogs() {
    try {
      // Rotate existing log files
      for (let i = this.maxFiles - 1; i > 0; i--) {
        const oldPath = `${this.logFile}.${i}`;
        const newPath = `${this.logFile}.${i + 1}`;
        if (require('fs').existsSync(oldPath)) {
          require('fs').renameSync(oldPath, newPath);
        }
      }

      // Rename current log file
      if (require('fs').existsSync(this.logFile)) {
        require('fs').renameSync(this.logFile, `${this.logFile}.1`);
      }

      // Create new log file
      this.stream.end();
      this.stream = createWriteStream(this.logFile, { flags: 'a' });
      this.currentFileSize = 0;
    } catch (err) {
      console.error('Failed to rotate logs:', err);
    }
  }

  private formatLogEntry(entry: LogEntry): string {
    const timestamp = new Date().toISOString();
    const level = entry.level.toUpperCase();
    const message = entry.message;
    const data = entry.data ? JSON.stringify(entry.data) : '';
    const error = entry.error ? `\nError: ${entry.error.stack || entry.error.message}` : '';
    const context = entry.context ? `\nContext: ${JSON.stringify(entry.context)}` : '';

    return `[${timestamp}] ${level}: ${message}${data}${error}${context}\n`;
  }

  private writeLog(entry: LogEntry) {
    const logEntry = this.formatLogEntry(entry);
    this.currentFileSize += Buffer.byteLength(logEntry);

    if (this.currentFileSize > this.maxFileSize) {
      this.rotateLogs();
    }

    this.stream.write(logEntry);
  }

  public debug(message: string, data?: any, context?: Record<string, any>) {
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'debug',
      message,
      data,
      context,
    });
  }

  public info(message: string, data?: any, context?: Record<string, any>) {
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'info',
      message,
      data,
      context,
    });
  }

  public warn(message: string, data?: any, context?: Record<string, any>) {
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'warn',
      message,
      data,
      context,
    });
  }

  public error(message: string, error?: Error, data?: any, context?: Record<string, any>) {
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'error',
      message,
      error,
      data,
      context,
    });
  }

  public fatal(message: string, error?: Error, data?: any, context?: Record<string, any>) {
    this.writeLog({
      timestamp: new Date().toISOString(),
      level: 'fatal',
      message,
      error,
      data,
      context,
    });
  }

  public getLogs(): LogEntry[] {
    try {
      const content = require('fs').readFileSync(this.logFile, 'utf8');
      return content
        .split('\n')
        .filter(Boolean)
        .map((line: string) => {
          const match = line.match(/\[(.*?)\] (\w+): (.*)/);
          if (match) {
            const [, timestamp, level, message] = match;
            return {
              timestamp,
              level: level.toLowerCase() as LogLevel,
              message,
            };
          }
          return null;
        })
        .filter(Boolean) as LogEntry[];
    } catch {
      return [];
    }
  }

  public clearLogs() {
    try {
      this.stream.end();
      this.stream = createWriteStream(this.logFile, { flags: 'w' });
      this.currentFileSize = 0;
    } catch (err) {
      console.error('Failed to clear logs:', err);
    }
  }

  public close() {
    this.stream.end();
  }
}

export const logger = new Logger();

export const setupLogging = () => {
  // Log uncaught exceptions
  process.on('uncaughtException', error => {
    logger.error('Uncaught Exception', error);
  });

  // Log unhandled promise rejections
  process.on('unhandledRejection', reason => {
    logger.error(
      'Unhandled Promise Rejection',
      reason instanceof Error ? reason : new Error(String(reason))
    );
  });

  // Log app events
  app.on('ready', () => {
    logger.info('Application ready');
  });

  app.on('window-all-closed', () => {
    logger.info('All windows closed');
  });

  app.on('activate', () => {
    logger.info('Application activated');
  });

  app.on('quit', () => {
    logger.info('Application quitting');
    logger.close();
  });

  // Log memory usage
  setInterval(() => {
    const memoryUsage = process.memoryUsage();
    logger.debug('Memory Usage', memoryUsage);
  }, 30000); // Every 30 seconds

  // Log CPU usage
  setInterval(() => {
    const cpuUsage = process.cpuUsage();
    logger.debug('CPU Usage', cpuUsage);
  }, 30000); // Every 30 seconds
};

export const logError = (error: Error, context?: Record<string, any>) => {
  logger.error(error.message, { error, context });
};

export const logWarning = (message: string, data?: any, context?: Record<string, any>) => {
  logger.warn(message, { data, context });
};

export const logInfo = (message: string, data?: any, context?: Record<string, any>) => {
  logger.info(message, { data, context });
};

export const logDebug = (message: string, data?: any, context?: Record<string, any>) => {
  logger.debug(message, data, context);
};

export const logFatal = (
  message: string,
  error?: Error,
  data?: any,
  context?: Record<string, any>
) => {
  logger.fatal(message, error, data, context);
};
