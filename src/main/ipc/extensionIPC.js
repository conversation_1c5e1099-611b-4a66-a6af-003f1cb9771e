const { ipcMain, dialog } = require('electron');

const errorHandler = require('../utils/errorHandling');
// const logger = require('../utils/logger'); // Logger is now global

let mainWindowInstance = null;

/**
 * Initializes IPC handlers for extension-related operations.
 * @param {Electron.BrowserWindow} mainWindow - The main browser window instance.
 */
function initializeExtensionIPC(mainWindow) {
  mainWindowInstance = mainWindow;

  /**
   * Handles requests from extensions for permissions.
   * Displays a dialog to the user to grant or deny requested permissions.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {object} payload - The payload containing extension details and requested permissions.
   * @param {string} payload.extensionId - The ID of the requesting extension.
   * @param {string[]} payload.permissions - An array of requested permissions.
   * @param {string} [payload.extensionName] - The name of the requesting extension (optional).
   * @returns {Promise<{granted: string[], denied: string[]}>} - A promise that resolves with granted and denied permissions.
   */
  ipcMain.handle(
    'request-extension-permission',
    errorHandler.wrapAsync(async (event, { extensionId, permissions, extensionName }) => {
      if (!mainWindowInstance) {
        logger.error('Cannot request permission, mainWindow is not available.');
        return { granted: [], denied: permissions };
      }

      const permissionDescriptions = {
        tabs: 'Manage tabs and view their content.',
        storage: 'Store and access extension data.',
        notifications: 'Display notifications.',
        history: 'Read and modify browser history.',
        bookmarks: 'Read and modify bookmarks.',
        activeTab: 'Access the content of the active tab upon user interaction.',
        // TODO: Add descriptions for other permissions as they are implemented
      };

      const requestedPermissionsList = permissions
        .map(p => `- ${permissionDescriptions[p] || p}`)
        .join('\n');

      const result = await dialog.showMessageBox(mainWindowInstance, {
        type: 'question',
        buttons: ['Deny All', 'Allow All'], // Could add 'Allow selectively', but that would complicate the UI
        defaultId: 0,
        title: 'Extension Permission Request',
        message: `Extension "${extensionName || extensionId}" requests the following permissions:`,
        detail: requestedPermissionsList,
        cancelId: 0, // If the user closes the dialog, assume all are denied
        noLink: true,
      });

      if (result.response === 1) {
        // User clicked 'Allow All'
        logger.info(
          `User granted permissions [${permissions.join(', ')}] to extension ${extensionId}`
        );
        return { granted: permissions, denied: [] };
      } else {
        // User clicked 'Deny All' or closed the dialog
        logger.info(
          `User denied permissions [${permissions.join(', ')}] to extension ${extensionId}`
        );
        return { granted: [], denied: permissions };
      }
    }, 'IPC:request-extension-permission')
  );
}

module.exports = { initializeExtensionIPC };
