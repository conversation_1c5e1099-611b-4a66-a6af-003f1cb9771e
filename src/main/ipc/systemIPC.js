const { app } = require('electron');

const errorHandler = require('../utils/errorHandling');

/**
 * Initializes IPC handlers related to system information and actions.
 * @param {IPCManager} ipcManager - The IPC manager instance to register handlers with.
 */
function initializeSystemIPC(ipcManager) {
  /**
   * <PERSON>les requests to get the application version.
   * @returns {string} The current application version.
   */
  ipcManager.handle(
    'get-app-version',
    errorHandler.wrapSync(() => {
      return app.getVersion();
    }, 'IPC:get-app-version')
  );

  /**
   * Handles requests to get the operating system platform.
   * @returns {string} The current operating system platform (e.g., 'darwin', 'win32', 'linux').
   */
  ipcManager.handle(
    'get-platform',
    errorHandler.wrapSync(() => {
      return process.platform;
    }, 'IPC:get-platform')
  );

  /**
   * <PERSON>les requests to open an external link in the default browser.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} url - The URL to open.
   */
  ipcManager.handle(
    'open-external-link',
    errorHandler.wrapSync((event, url) => {
      require('electron').shell.openExternal(url);
    }, 'IPC:open-external-link')
  );
}

module.exports = { initializeSystemIPC };
