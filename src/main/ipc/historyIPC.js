const { ipcMain } = require('electron');

const errorHandler = require('../utils/errorHandling');

let configManagerInstance = null;

/**
 * Initializes IPC handlers for history-related operations.
 * @param {ConfigManager} configManager - The ConfigManager instance for managing history data.
 */
function initializeHistoryIPC(configManager) {
  configManagerInstance = configManager;

  /**
   * <PERSON><PERSON> requests to get the entire browsing history.
   * @returns {Array<object>} - An array of history items.
   */
  ipcMain.handle(
    'get-history',
    errorHandler.wrapSync(() => {
      const settings = configManagerInstance.getAll();
      return settings.history || [];
    }, 'IPC:get-history')
  );

  /**
   * <PERSON><PERSON> requests to add a new item to the browsing history.
   * If an item with the same URL already exists, it updates the existing entry.
   * Limits the history to 1000 entries, keeping the most recent ones.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {object} historyItem - The history item to add or update.
   * @param {string} historyItem.url - The URL of the history item.
   * @param {string} historyItem.title - The title of the history item.
   * @param {number} historyItem.timestamp - The timestamp of the history item.
   * @returns {boolean} - True if the operation was successful.
   */
  ipcMain.handle(
    'add-history-item',
    errorHandler.wrapSync((event, historyItem) => {
      const settings = configManagerInstance.getAll();
      const history = settings.history || [];

      // Check if such an entry already exists
      const existingIndex = history.findIndex(item => item.url === historyItem.url);

      if (existingIndex !== -1) {
        // Update the existing entry
        history[existingIndex] = historyItem;
      } else {
        // Add a new entry
        history.push(historyItem);
      }

      // Limit history to 1000 entries
      if (history.length > 1000) {
        history.sort((a, b) => b.timestamp - a.timestamp); // Sort by timestamp descending
        history.splice(1000); // Keep only the first 1000 (most recent)
      }

      // Save the updated history
      configManagerInstance.set('history', history);

      return true;
    }, 'IPC:add-history-item')
  );

  /**
   * Handles requests to remove a specific item from the browsing history by its ID.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {string} itemId - The ID of the history item to remove.
   * @returns {boolean} - True if the operation was successful.
   */
  ipcMain.handle(
    'remove-history-item',
    errorHandler.wrapSync((event, itemId) => {
      const settings = configManagerInstance.getAll();
      let history = settings.history || [];
      history = history.filter(item => item.id !== itemId);
      configManagerInstance.set('history', history);
      return true;
    }, 'IPC:remove-history-item')
  );

  /**
   * Handles requests to update an existing history item.
   * @param {Electron.IpcMainEvent} event - The IPC event object.
   * @param {object} updatedItem - The updated history item.
   * @param {string} updatedItem.id - The ID of the history item to update.
   * @returns {boolean} - True if the update was successful, false otherwise.
   */
  ipcMain.handle(
    'update-history-item',
    errorHandler.wrapSync((event, updatedItem) => {
      const settings = configManagerInstance.getAll();
      const history = settings.history || [];
      const index = history.findIndex(item => item.id === updatedItem.id);
      if (index !== -1) {
        history[index] = updatedItem;
        configManagerInstance.set('history', history);
        return true;
      }
      return false;
    }, 'IPC:update-history-item')
  );

  /**
   * Handles requests to clear the entire browsing history.
   * @returns {boolean} - True if the operation was successful.
   */
  ipcMain.handle(
    'clear-history',
    errorHandler.wrapSync(() => {
      configManagerInstance.set('history', []);
      return true;
    }, 'IPC:clear-history')
  );
}

module.exports = { initializeHistoryIPC };
