import log from 'electron-log';
import { BrowserWindow } from 'electron';
import { autoUpdater } from 'electron-updater';
import { IpcChannels } from '../../shared/ipc-channels';

export class UpdateManager {
  private mainWindow: BrowserWindow | null = null;

  constructor() {
    log.transports.file.level = 'info';
    autoUpdater.logger = log;
    // Disable auto-download, we want to control this process
    autoUpdater.autoDownload = false;

    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for update...');
    });

    autoUpdater.on('update-available', info => {
      log.info('Update available.', info);
      this.mainWindow?.webContents.send(IpcChannels.UPDATE_AVAILABLE);
    });

    autoUpdater.on('update-not-available', info => {
      log.info('Update not available.', info);
    });

    autoUpdater.on('update-downloaded', () => {
      log.info('Update downloaded.');
      this.mainWindow?.webContents.send(IpcChannels.UPDATE_DOWNLOADED);
    });

    autoUpdater.on('error', err => {
      log.error('Error in auto-updater. ' + err);
    });
  }

  public init(mainWindow: BrowserWindow): void {
    this.mainWindow = mainWindow;
  }

  public checkForUpdates(): void {
    autoUpdater.checkForUpdatesAndNotify().catch(err => {
      log.error('Failed to check for updates:', err);
    });
  }
}