import { EventEmitter } from 'events';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';

export interface LogEntry {
  id: string;
  level: LogLevel;
  message: string;
  timestamp: number;
  correlationId?: string;
  context?: Record<string, any>;
  stack?: string;
  module?: string;
  userId?: string;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  maxEntries: number;
  remoteEndpoint?: string;
  bufferSize: number;
  flushInterval: number;
  enableStructuredLogging: boolean;
  enablePerformanceLogging: boolean;
}

export class EnhancedLogger extends EventEmitter {
  private static instance: EnhancedLogger;
  private config: LoggerConfig;
  private logs: LogEntry[] = [];
  private buffer: LogEntry[] = [];
  private flushTimer: NodeJS.Timeout | null = null;
  private correlationId: string | null = null;
  private sessionId: string;

  private readonly levelPriority: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4,
  };

  private constructor(config: Partial<LoggerConfig> = {}) {
    super();
    this.config = {
      level: 'info',
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      maxEntries: 1000,
      bufferSize: 50,
      flushInterval: 5000,
      enableStructuredLogging: true,
      enablePerformanceLogging: true,
      ...config,
    };

    this.sessionId = this.generateSessionId();
    this.startFlushTimer();
  }

  public static getInstance(config?: Partial<LoggerConfig>): EnhancedLogger {
    if (!EnhancedLogger.instance) {
      EnhancedLogger.instance = new EnhancedLogger(config);
    }
    return EnhancedLogger.instance;
  }

  public setCorrelationId(id: string): void {
    this.correlationId = id;
  }

  public debug(
    message: string,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    this.log('debug', message, context, metadata);
  }

  public info(
    message: string,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    this.log('info', message, context, metadata);
  }

  public warn(
    message: string,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    this.log('warn', message, context, metadata);
  }

  public error(
    message: string,
    error?: Error,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    const logContext = {
      ...context,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    };
    this.log('error', message, logContext, metadata);
  }

  public fatal(
    message: string,
    error?: Error,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    const logContext = {
      ...context,
      error: error
        ? {
            name: error.name,
            message: error.message,
            stack: error.stack,
          }
        : undefined,
    };
    this.log('fatal', message, logContext, metadata);
  }

  public performance(operation: string, duration: number, context?: Record<string, any>): void {
    if (!this.config.enablePerformanceLogging) return;

    this.log('info', `Performance: ${operation}`, {
      ...context,
      performance: {
        operation,
        duration,
        timestamp: Date.now(),
      },
    });
  }

  public startTimer(operation: string): () => void {
    const startTime = performance.now();
    return () => {
      const duration = performance.now() - startTime;
      this.performance(operation, duration);
    };
  }

  private log(
    level: LogLevel,
    message: string,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    if (this.levelPriority[level] < this.levelPriority[this.config.level]) {
      return;
    }

    const entry: LogEntry = {
      id: this.generateLogId(),
      level,
      message,
      timestamp: Date.now(),
      correlationId: this.correlationId || undefined,
      context,
      module: this.getCallerModule(),
      userId: this.getUserId(),
      sessionId: this.sessionId,
      metadata,
    };

    // Add to logs array
    this.logs.push(entry);

    // Add to buffer for remote logging
    if (this.config.enableRemote) {
      this.buffer.push(entry);
    }

    // Log to console
    if (this.config.enableConsole) {
      this.logToConsole(entry);
    }

    // Store in local storage
    if (this.config.enableStorage) {
      this.storeLog(entry);
    }

    // Emit log event
    this.emit('log', entry);

    // Trim logs if needed
    if (this.logs.length > this.config.maxEntries) {
      this.logs = this.logs.slice(-this.config.maxEntries);
    }

    // Flush buffer if needed
    if (this.buffer.length >= this.config.bufferSize) {
      this.flushBuffer();
    }
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString();
    const prefix = `[${timestamp}] [${entry.level.toUpperCase()}] [${entry.module || 'Unknown'}]`;

    if (this.config.enableStructuredLogging) {
      const logData = {
        ...entry,
        timestamp: new Date(entry.timestamp).toISOString(),
      };

      switch (entry.level) {
        case 'debug':
          console.debug(prefix, entry.message, logData);
          break;
        case 'info':
          console.info(prefix, entry.message, logData);
          break;
        case 'warn':
          console.warn(prefix, entry.message, logData);
          break;
        case 'error':
        case 'fatal':
          console.error(prefix, entry.message, logData);
          break;
      }
    } else {
      const message = `${prefix} ${entry.message}`;
      switch (entry.level) {
        case 'debug':
          console.debug(message);
          break;
        case 'info':
          console.info(message);
          break;
        case 'warn':
          console.warn(message);
          break;
        case 'error':
        case 'fatal':
          console.error(message);
          break;
      }
    }
  }

  private storeLog(entry: LogEntry): void {
    try {
      const stored = localStorage.getItem('browser_logs') || '[]';
      const logs = JSON.parse(stored);
      logs.push(entry);

      // Keep only last 500 logs in storage
      const trimmedLogs = logs.slice(-500);
      localStorage.setItem('browser_logs', JSON.stringify(trimmedLogs));
    } catch (e) {
      console.warn('Failed to store log entry:', e);
    }
  }

  private flushBuffer(): void {
    if (this.buffer.length === 0 || !this.config.remoteEndpoint) return;

    const logsToSend = [...this.buffer];
    this.buffer = [];

    fetch(this.config.remoteEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        logs: logsToSend,
        sessionId: this.sessionId,
        timestamp: Date.now(),
      }),
    }).catch(error => {
      console.warn('Failed to send logs to remote endpoint:', error);
      // Re-add logs to buffer for retry
      this.buffer.unshift(...logsToSend);
    });
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flushBuffer();
    }, this.config.flushInterval);
  }

  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCallerModule(): string {
    const stack = new Error().stack;
    if (!stack) return 'Unknown';

    const lines = stack.split('\n');
    // Skip the first few lines (Error, this method, log method)
    for (let i = 3; i < lines.length; i++) {
      const line = lines[i];
      const match = line.match(/at\s+(.+?)\s+\(/);
      if (match) {
        return match[1];
      }
    }
    return 'Unknown';
  }

  private getUserId(): string | undefined {
    // This would typically come from your auth system
    return undefined;
  }

  public getLogs(level?: LogLevel, limit?: number): LogEntry[] {
    let filteredLogs = this.logs;

    if (level) {
      filteredLogs = this.logs.filter(log => log.level === level);
    }

    if (limit) {
      filteredLogs = filteredLogs.slice(-limit);
    }

    return filteredLogs;
  }

  public clearLogs(): void {
    this.logs = [];
    this.buffer = [];
    localStorage.removeItem('browser_logs');
  }

  public updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };

    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.startFlushTimer();
    }
  }

  public destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
    }
    this.flushBuffer();
    this.removeAllListeners();
  }
}

// Export singleton instance
export const logger = EnhancedLogger.getInstance();
