import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';

import { app } from 'electron';
import { v4 as uuidv4 } from 'uuid';

// TODO: Install @types/uuid to fix missing type declarations error
import { IndexManager } from './bookmarks/IndexManager';

export interface Bookmark {
  id: string;
  title: string;
  url: string;
  favicon?: string;
  description?: string;
  tags: string[];
  createdAt: number;
  updatedAt: number;
  lastVisited?: number;
  visitCount: number;
  parentId?: string;
  children?: Bookmark[];
  isFolder: boolean;
  order: number;
  color?: string;
  notes?: string;
  customIcon?: string;
  syncStatus: 'synced' | 'pending' | 'error';
}

export interface BookmarkFolder extends Bookmark {
  isFolder: true;
  children: Bookmark[];
  expanded?: boolean;
}

export interface BookmarkConfig {
  autoBackup: boolean;
  backupInterval: number;
  maxBackups: number;
  syncEnabled: boolean;
  syncInterval: number;
  defaultFolderId: string;
  sortBy: 'title' | 'createdAt' | 'visits' | 'custom';
  sortOrder: 'asc' | 'desc';
  showFavicons: boolean;
  showDescriptions: boolean;
  showTags: boolean;
  showVisitCount: boolean;
  showLastVisited: boolean;
  maxRecentBookmarks: number;
  maxSearchResults: number;
  enableTags: boolean;
  enableNotes: boolean;
  enableCustomIcons: boolean;
  enableColors: boolean;
  enableFolders: boolean;
  enableImport: boolean;
  enableExport: boolean;
  enableSharing: boolean;
  enableSync: boolean;
  enableBackup: boolean;
  enableRestore: boolean;
  enableSearch: boolean;
  enableSorting: boolean;
  enableFiltering: boolean;
  enableGrouping: boolean;
  enableStatistics: boolean;
  enableAnalytics: boolean;
  enableNotifications: boolean;
  enableShortcuts: boolean;
  enableDragAndDrop: boolean;
  enableContextMenu: boolean;
  enableToolbar: boolean;
  enableSidebar: boolean;
  enableBookmarkBar: boolean;
  enableQuickAdd: boolean;
  enableQuickEdit: boolean;
  enableQuickDelete: boolean;
  enableQuickMove: boolean;
  enableQuickCopy: boolean;
  enableQuickPaste: boolean;
  enableQuickSearch: boolean;
  enableQuickFilter: boolean;
  enableQuickSort: boolean;
  enableQuickGroup: boolean;
  enableQuickStats: boolean;
  enableQuickAnalytics: boolean;
  enableQuickNotifications: boolean;
  enableQuickShortcuts: boolean;
  enableQuickDragAndDrop: boolean;
  enableQuickContextMenu: boolean;
  enableQuickToolbar: boolean;
  enableQuickSidebar: boolean;
  enableQuickBookmarkBar: boolean;
}

export class BookmarkManager extends EventEmitter {
  private static instance: BookmarkManager;
  private bookmarks: Map<string, Bookmark>;
  private config: BookmarkConfig;
  private isInitialized: boolean;
  private backupPath: string;
  private syncPath: string;
  private lastBackup: number;
  private lastSync: number;
  private indexManager: IndexManager;
  private recentBookmarks: Bookmark[];
  private popularBookmarks: Bookmark[];
  private recentlyVisited: Bookmark[];
  private frequentlyVisited: Bookmark[];
  private recentlyAdded: Bookmark[];
  private recentlyUpdated: Bookmark[];
  private recentlyDeleted: Bookmark[];
  private recentlyMoved: Bookmark[];
  private recentlyCopied: Bookmark[];
  private recentlyPasted: Bookmark[];
  private recentlySearched: Bookmark[];
  private recentlyFiltered: Bookmark[];
  private recentlySorted: Bookmark[];
  private recentlyGrouped: Bookmark[];
  private recentlyStatted: Bookmark[];
  private recentlyAnalyzed: Bookmark[];
  private recentlyNotified: Bookmark[];
  private recentlyShortcutted: Bookmark[];
  private recentlyDraggedAndDropped: Bookmark[];
  private recentlyContextMenued: Bookmark[];
  private recentlyToolbarred: Bookmark[];
  private recentlySidebarred: Bookmark[];
  private recentlyBookmarkBarred: Bookmark[];
  private recentlyQuickAdded: Bookmark[];
  private recentlyQuickEdited: Bookmark[];
  private recentlyQuickDeleted: Bookmark[];
  private recentlyQuickMoved: Bookmark[];
  private recentlyQuickCopied: Bookmark[];
  private recentlyQuickPasted: Bookmark[];
  private recentlyQuickSearched: Bookmark[];
  private recentlyQuickFiltered: Bookmark[];
  private recentlyQuickSorted: Bookmark[];
  private recentlyQuickGrouped: Bookmark[];
  private recentlyQuickStatted: Bookmark[];
  private recentlyQuickAnalyzed: Bookmark[];
  private recentlyQuickNotified: Bookmark[];
  private recentlyQuickShortcutted: Bookmark[];
  private recentlyQuickDraggedAndDropped: Bookmark[];
  private recentlyQuickContextMenued: Bookmark[];
  private recentlyQuickToolbarred: Bookmark[];
  private recentlyQuickSidebarred: Bookmark[];
  private recentlyQuickBookmarkBarred: Bookmark[];

  private constructor() {
    super();
    this.bookmarks = new Map();
    this.config = this.getDefaultConfig();
    this.isInitialized = false;
    this.indexManager = new IndexManager();
    this.backupManager = new BackupManager(
      path.join(app.getPath('userData'), 'bookmarks-backup'),
      this.config
    );
    this.syncManager = new SyncManager(
      path.join(app.getPath('userData'), 'bookmarks-sync'),
      this.config
    );
    this.recentBookmarks = [];
    this.popularBookmarks = [];
    this.recentlyVisited = [];
    this.frequentlyVisited = [];
    this.recentlyAdded = [];
    this.recentlyUpdated = [];
    this.recentlyDeleted = [];
    this.recentlyMoved = [];
    this.recentlyCopied = [];
    this.recentlyPasted = [];
    this.recentlySearched = [];
    this.recentlyFiltered = [];
    this.recentlySorted = [];
    this.recentlyGrouped = [];
    this.recentlyStatted = [];
    this.recentlyAnalyzed = [];
    this.recentlyNotified = [];
    this.recentlyShortcutted = [];
    this.recentlyDraggedAndDropped = [];
    this.recentlyContextMenued = [];
    this.recentlyToolbarred = [];
    this.recentlySidebarred = [];
    this.recentlyBookmarkBarred = [];
    this.recentlyQuickAdded = [];
    this.recentlyQuickEdited = [];
    this.recentlyQuickDeleted = [];
    this.recentlyQuickMoved = [];
    this.recentlyQuickCopied = [];
    this.recentlyQuickPasted = [];
    this.recentlyQuickSearched = [];
    this.recentlyQuickFiltered = [];
    this.recentlyQuickSorted = [];
    this.recentlyQuickGrouped = [];
    this.recentlyQuickStatted = [];
    this.recentlyQuickAnalyzed = [];
    this.recentlyQuickNotified = [];
    this.recentlyQuickShortcutted = [];
    this.recentlyQuickDraggedAndDropped = [];
    this.recentlyQuickContextMenued = [];
    this.recentlyQuickToolbarred = [];
    this.recentlyQuickSidebarred = [];
    this.recentlyQuickBookmarkBarred = [];
    this.initialize();
  }

  public static getInstance(): BookmarkManager {
    if (!BookmarkManager.instance) {
      BookmarkManager.instance = new BookmarkManager();
    }
    return BookmarkManager.instance;
  }

  private getDefaultConfig(): BookmarkConfig {
    return {
      autoBackup: true,
      backupInterval: 24 * 60 * 60 * 1000, // 24 hours
      maxBackups: 10,
      syncEnabled: true,
      syncInterval: 15 * 60 * 1000, // 15 minutes
      defaultFolderId: 'root',
      sortBy: 'title',
      sortOrder: 'asc',
      showFavicons: true,
      showDescriptions: true,
      showTags: true,
      showVisitCount: true,
      showLastVisited: true,
      maxRecentBookmarks: 20,
      maxSearchResults: 50,
      enableTags: true,
      enableNotes: true,
      enableCustomIcons: true,
      enableColors: true,
      enableFolders: true,
      enableImport: true,
      enableExport: true,
      enableSharing: true,
      enableSync: true,
      enableBackup: true,
      enableRestore: true,
      enableSearch: true,
      enableSorting: true,
      enableFiltering: true,
      enableGrouping: true,
      enableStatistics: true,
      enableAnalytics: true,
      enableNotifications: true,
      enableShortcuts: true,
      enableDragAndDrop: true,
      enableContextMenu: true,
      enableToolbar: true,
      enableSidebar: true,
      enableBookmarkBar: true,
      enableQuickAdd: true,
      enableQuickEdit: true,
      enableQuickDelete: true,
      enableQuickMove: true,
      enableQuickCopy: true,
      enableQuickPaste: true,
      enableQuickSearch: true,
      enableQuickFilter: true,
      enableQuickSort: true,
      enableQuickGroup: true,
      enableQuickStats: true,
      enableQuickAnalytics: true,
      enableQuickNotifications: true,
      enableQuickShortcuts: true,
      enableQuickDragAndDrop: true,
      enableQuickContextMenu: true,
      enableQuickToolbar: true,
      enableQuickSidebar: true,
      enableQuickBookmarkBar: true,
    };
  }

  private async initialize(): Promise<void> {
    try {
      await this.loadConfig();
      await this.loadBookmarks();
      await this.setupBackup();
      await this.setupSync();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize bookmark manager:', error);
      this.emit('initialization-error', error);
    }
  }

  private async loadConfig(): Promise<void> {
    try {
      const configPath = path.join(app.getPath('userData'), 'bookmark-config.json');
      const data = await fs.readFile(configPath, 'utf-8');
      this.config = { ...this.getDefaultConfig(), ...JSON.parse(data) };
    } catch (error) {
      console.error('Failed to load bookmark config:', error);
    }
  }

  private async loadBookmarks(): Promise<void> {
    try {
      const bookmarksPath = path.join(app.getPath('userData'), 'bookmarks.json');
      const data = await fs.readFile(bookmarksPath, 'utf-8');
      const bookmarks = JSON.parse(data);
      this.bookmarks = new Map(Object.entries(bookmarks));
      this.updateIndexes();
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
    }
  }

  private async setupBackup(): Promise<void> {
    if (this.config.autoBackup) {
      setInterval(async () => {
        if (Date.now() - this.lastBackup >= this.config.backupInterval) {
          await this.backup();
        }
      }, this.config.backupInterval);
    }
  }

  private async setupSync(): Promise<void> {
    if (this.config.syncEnabled) {
      setInterval(async () => {
        if (Date.now() - this.lastSync >= this.config.syncInterval) {
          await this.sync();
        }
      }, this.config.syncInterval);
    }
  }

  private updateIndexes(): void {
    this.indexManager.updateIndexes(this.bookmarks);
  }

  private getSearchTerms(bookmark: Bookmark): string[] {
    const terms = new Set<string>();

    // Add title terms
    bookmark.title
      .toLowerCase()
      .split(/\s+/)
      .forEach(term => terms.add(term));

    // Add URL terms
    bookmark.url
      .toLowerCase()
      .split(/[/?#&.=]/)
      .forEach(term => {
        if (term) terms.add(term);
      });

    // Add description terms
    if (bookmark.description) {
      bookmark.description
        .toLowerCase()
        .split(/\s+/)
        .forEach(term => terms.add(term));
    }

    // Add notes terms
    if (bookmark.notes) {
      bookmark.notes
        .toLowerCase()
        .split(/\s+/)
        .forEach(term => terms.add(term));
    }

    // Add tags
    bookmark.tags.forEach(tag => terms.add(tag.toLowerCase()));

    return Array.from(terms);
  }

  public async addBookmark(
    bookmark: Omit<Bookmark, 'id' | 'createdAt' | 'updatedAt' | 'visitCount' | 'syncStatus'>
  ): Promise<Bookmark> {
    const id = uuidv4();
    const newBookmark: Bookmark = {
      ...bookmark,
      id,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      visitCount: 0,
      syncStatus: 'pending',
    };

    this.bookmarks.set(id, newBookmark);
    this.updateIndexes();
    this.recentlyAdded.unshift(newBookmark);
    this.recentlyAdded = this.recentlyAdded.slice(0, this.config.maxRecentBookmarks);

    await this.saveBookmarks();
    this.emit('bookmark-added', newBookmark);

    return newBookmark;
  }

  public async updateBookmark(id: string, updates: Partial<Bookmark>): Promise<Bookmark> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) {
      throw new Error(`Bookmark with id ${id} not found`);
    }

    const updatedBookmark: Bookmark = {
      ...bookmark,
      ...updates,
      updatedAt: Date.now(),
      syncStatus: 'pending',
    };

    this.bookmarks.set(id, updatedBookmark);
    this.updateIndexes();
    this.recentlyUpdated.unshift(updatedBookmark);
    this.recentlyUpdated = this.recentlyUpdated.slice(0, this.config.maxRecentBookmarks);

    await this.saveBookmarks();
    this.emit('bookmark-updated', updatedBookmark);

    return updatedBookmark;
  }

  public async deleteBookmark(id: string): Promise<void> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) {
      throw new Error(`Bookmark with id ${id} not found`);
    }

    this.bookmarks.delete(id);
    this.updateIndexes();
    this.recentlyDeleted.unshift(bookmark);
    this.recentlyDeleted = this.recentlyDeleted.slice(0, this.config.maxRecentBookmarks);

    await this.saveBookmarks();
    this.emit('bookmark-deleted', bookmark);
  }

  public async moveBookmark(id: string, newParentId: string): Promise<Bookmark> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) {
      throw new Error(`Bookmark with id ${id} not found`);
    }

    const updatedBookmark: Bookmark = {
      ...bookmark,
      parentId: newParentId,
      updatedAt: Date.now(),
      syncStatus: 'pending',
    };

    this.bookmarks.set(id, updatedBookmark);
    this.updateIndexes();
    this.recentlyMoved.unshift(updatedBookmark);
    this.recentlyMoved = this.recentlyMoved.slice(0, this.config.maxRecentBookmarks);

    await this.saveBookmarks();
    this.emit('bookmark-moved', updatedBookmark);

    return updatedBookmark;
  }

  public async copyBookmark(id: string, newParentId: string): Promise<Bookmark> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) {
      throw new Error(`Bookmark with id ${id} not found`);
    }

    const newId = uuidv4();
    const copiedBookmark: Bookmark = {
      ...bookmark,
      id: newId,
      parentId: newParentId,
      createdAt: Date.now(),
      updatedAt: Date.now(),
      visitCount: 0,
      syncStatus: 'pending',
    };

    this.bookmarks.set(newId, copiedBookmark);
    this.updateIndexes();
    this.recentlyCopied.unshift(copiedBookmark);
    this.recentlyCopied = this.recentlyCopied.slice(0, this.config.maxRecentBookmarks);

    await this.saveBookmarks();
    this.emit('bookmark-copied', copiedBookmark);

    return copiedBookmark;
  }

  public async pasteBookmark(id: string, targetId: string): Promise<Bookmark> {
    const bookmark = this.bookmarks.get(id);
    if (!bookmark) {
      throw new Error(`Bookmark with id ${id} not found`);
    }

    const updatedBookmark: Bookmark = {
      ...bookmark,
      parentId: targetId,
      updatedAt: Date.now(),
      syncStatus: 'pending',
    };

    this.bookmarks.set(id, updatedBookmark);
    this.updateIndexes();
    this.recentlyPasted.unshift(updatedBookmark);
    this.recentlyPasted = this.recentlyPasted.slice(0, this.config.maxRecentBookmarks);

    await this.saveBookmarks();
    this.emit('bookmark-pasted', updatedBookmark);

    return updatedBookmark;
  }

  public searchBookmarks(query: string): Bookmark[] {
    const bookmarks = this.indexManager.searchBookmarks(
      query,
      this.bookmarks,
      this.config.maxSearchResults
    );

    this.recentlySearched.unshift(...bookmarks);
    this.recentlySearched = this.recentlySearched.slice(0, this.config.maxRecentBookmarks);

    return bookmarks;
  }

  public filterBookmarks(filters: Partial<Bookmark>): Bookmark[] {
    const results = Array.from(this.bookmarks.values()).filter(bookmark => {
      return Object.entries(filters).every(([key, value]) => {
        return bookmark[key as keyof Bookmark] === value;
      });
    });

    this.recentlyFiltered.unshift(...results);
    this.recentlyFiltered = this.recentlyFiltered.slice(0, this.config.maxRecentBookmarks);

    return results;
  }

  public sortBookmarks(
    bookmarks: Bookmark[],
    sortBy: keyof Bookmark = this.config.sortBy,
    sortOrder: 'asc' | 'desc' = this.config.sortOrder
  ): Bookmark[] {
    const sorted = [...bookmarks].sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortOrder === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    });

    this.recentlySorted.unshift(...sorted);
    this.recentlySorted = this.recentlySorted.slice(0, this.config.maxRecentBookmarks);

    return sorted;
  }

  public groupBookmarks(bookmarks: Bookmark[], groupBy: keyof Bookmark): Map<string, Bookmark[]> {
    const groups = new Map<string, Bookmark[]>();

    for (const bookmark of bookmarks) {
      const key = String(bookmark[groupBy]);
      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)?.push(bookmark);
    }

    this.recentlyGrouped.unshift(...bookmarks);
    this.recentlyGrouped = this.recentlyGrouped.slice(0, this.config.maxRecentBookmarks);

    return groups;
  }

  public getBookmarkStats(): {
    total: number;
    folders: number;
    urls: number;
    tags: number;
    visits: number;
    recent: number;
    popular: number;
  } {
    const stats = {
      total: this.bookmarks.size,
      folders: 0,
      urls: 0,
      tags: new Set<string>(),
      visits: 0,
      recent: 0,
      popular: 0,
    };

    for (const bookmark of this.bookmarks.values()) {
      if (bookmark.isFolder) {
        stats.folders++;
      } else {
        stats.urls++;
      }

      bookmark.tags.forEach(tag => stats.tags.add(tag));
      stats.visits += bookmark.visitCount;

      if (bookmark.lastVisited && Date.now() - bookmark.lastVisited < 24 * 60 * 60 * 1000) {
        stats.recent++;
      }

      if (bookmark.visitCount > 10) {
        stats.popular++;
      }
    }

    return {
      ...stats,
      tags: stats.tags.size,
    };
  }

  public getBookmarkAnalytics(): {
    visitsByDay: Map<string, number>;
    visitsByHour: Map<number, number>;
    visitsByTag: Map<string, number>;
    visitsByFolder: Map<string, number>;
    averageVisitsPerDay: number;
    mostVisitedBookmarks: Bookmark[];
    mostRecentBookmarks: Bookmark[];
    mostPopularTags: string[];
    mostPopularFolders: string[];
  } {
    const analytics = {
      visitsByDay: new Map<string, number>(),
      visitsByHour: new Map<number, number>(),
      visitsByTag: new Map<string, number>(),
      visitsByFolder: new Map<string, number>(),
      averageVisitsPerDay: 0,
      mostVisitedBookmarks: [] as Bookmark[],
      mostRecentBookmarks: [] as Bookmark[],
      mostPopularTags: [] as string[],
      mostPopularFolders: [] as string[],
    };

    for (const bookmark of this.bookmarks.values()) {
      if (bookmark.lastVisited) {
        const date = new Date(bookmark.lastVisited);
        const dayKey = date.toISOString().split('T')[0];
        const hour = date.getHours();

        analytics.visitsByDay.set(dayKey, (analytics.visitsByDay.get(dayKey) || 0) + 1);
        analytics.visitsByHour.set(hour, (analytics.visitsByHour.get(hour) || 0) + 1);
      }

      bookmark.tags.forEach(tag => {
        analytics.visitsByTag.set(tag, (analytics.visitsByTag.get(tag) || 0) + bookmark.visitCount);
      });

      if (bookmark.parentId) {
        analytics.visitsByFolder.set(
          bookmark.parentId,
          (analytics.visitsByFolder.get(bookmark.parentId) || 0) + bookmark.visitCount
        );
      }
    }

    analytics.mostVisitedBookmarks = Array.from(this.bookmarks.values())
      .sort((a, b) => b.visitCount - a.visitCount)
      .slice(0, 10);

    analytics.mostRecentBookmarks = Array.from(this.bookmarks.values())
      .filter(bookmark => bookmark.lastVisited)
      .sort((a, b) => (b.lastVisited || 0) - (a.lastVisited || 0))
      .slice(0, 10);

    analytics.mostPopularTags = Array.from(analytics.visitsByTag.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([tag]) => tag)
      .slice(0, 10);

    analytics.mostPopularFolders = Array.from(analytics.visitsByFolder.entries())
      .sort((a, b) => b[1] - a[1])
      .map(([folder]) => folder)
      .slice(0, 10);

    const totalVisits = Array.from(analytics.visitsByDay.values()).reduce(
      (sum, count) => sum + count,
      0
    );
    const daysWithVisits = analytics.visitsByDay.size;
    analytics.averageVisitsPerDay = daysWithVisits ? totalVisits / daysWithVisits : 0;

    this.recentlyAnalyzed.unshift(...analytics.mostRecentBookmarks);
    this.recentlyAnalyzed = this.recentlyAnalyzed.slice(0, this.config.maxRecentBookmarks);

    return analytics;
  }

  public async backup(): Promise<void> {
    try {
      const backupDir = path.join(this.backupPath, new Date().toISOString());
      await fs.mkdir(backupDir, { recursive: true });

      const bookmarksPath = path.join(backupDir, 'bookmarks.json');
      const configPath = path.join(backupDir, 'config.json');

      await fs.writeFile(
        bookmarksPath,
        JSON.stringify(Object.fromEntries(this.bookmarks), null, 2)
      );
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));

      this.lastBackup = Date.now();
      this.emit('backup-created', backupDir);
    } catch (error) {
      console.error('Failed to create backup:', error);
      this.emit('backup-error', error);
    }
  }

  public async restore(backupDir: string): Promise<void> {
    try {
      const bookmarksPath = path.join(backupDir, 'bookmarks.json');
      const configPath = path.join(backupDir, 'config.json');

      const bookmarksData = await fs.readFile(bookmarksPath, 'utf-8');
      const configData = await fs.readFile(configPath, 'utf-8');

      this.bookmarks = new Map(Object.entries(JSON.parse(bookmarksData)));
      this.config = { ...this.getDefaultConfig(), ...JSON.parse(configData) };

      this.updateIndexes();
      await this.saveBookmarks();
      this.emit('restore-completed', backupDir);
    } catch (error) {
      console.error('Failed to restore backup:', error);
      this.emit('restore-error', error);
    }
  }

  public async sync(): Promise<void> {
    try {
      const syncDir = path.join(this.syncPath, new Date().toISOString());
      await fs.mkdir(syncDir, { recursive: true });

      const bookmarksPath = path.join(syncDir, 'bookmarks.json');
      const configPath = path.join(syncDir, 'config.json');

      await fs.writeFile(
        bookmarksPath,
        JSON.stringify(Object.fromEntries(this.bookmarks), null, 2)
      );
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));

      this.lastSync = Date.now();
      this.emit('sync-completed', syncDir);
    } catch (error) {
      console.error('Failed to sync:', error);
      this.emit('sync-error', error);
    }
  }

  private async saveBookmarks(): Promise<void> {
    try {
      const bookmarksPath = path.join(app.getPath('userData'), 'bookmarks.json');
      await fs.writeFile(
        bookmarksPath,
        JSON.stringify(Object.fromEntries(this.bookmarks), null, 2)
      );
    } catch (error) {
      console.error('Failed to save bookmarks:', error);
      this.emit('save-error', error);
    }
  }

  public getConfig(): BookmarkConfig {
    return { ...this.config };
  }

  public async setConfig(newConfig: Partial<BookmarkConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    this.emit('config-updated', this.config);
  }

  private async saveConfig(): Promise<void> {
    try {
      const configPath = path.join(app.getPath('userData'), 'bookmark-config.json');
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('Failed to save bookmark config:', error);
      this.emit('config-save-error', error);
    }
  }

  public getInitializationStatus(): boolean {
    return this.isInitialized;
  }

  public getRecentBookmarks(): Bookmark[] {
    return [...this.recentBookmarks];
  }

  public getPopularBookmarks(): Bookmark[] {
    return [...this.popularBookmarks];
  }

  public getRecentlyVisited(): Bookmark[] {
    return [...this.recentlyVisited];
  }

  public getFrequentlyVisited(): Bookmark[] {
    return [...this.frequentlyVisited];
  }

  public getRecentlyAdded(): Bookmark[] {
    return [...this.recentlyAdded];
  }

  public getRecentlyUpdated(): Bookmark[] {
    return [...this.recentlyUpdated];
  }

  public getRecentlyDeleted(): Bookmark[] {
    return [...this.recentlyDeleted];
  }

  public getRecentlyMoved(): Bookmark[] {
    return [...this.recentlyMoved];
  }

  public getRecentlyCopied(): Bookmark[] {
    return [...this.recentlyCopied];
  }

  public getRecentlyPasted(): Bookmark[] {
    return [...this.recentlyPasted];
  }

  public getRecentlySearched(): Bookmark[] {
    return [...this.recentlySearched];
  }

  public getRecentlyFiltered(): Bookmark[] {
    return [...this.recentlyFiltered];
  }

  public getRecentlySorted(): Bookmark[] {
    return [...this.recentlySorted];
  }

  public getRecentlyGrouped(): Bookmark[] {
    return [...this.recentlyGrouped];
  }

  public getRecentlyStatted(): Bookmark[] {
    return [...this.recentlyStatted];
  }

  public getRecentlyAnalyzed(): Bookmark[] {
    return [...this.recentlyAnalyzed];
  }

  public getRecentlyNotified(): Bookmark[] {
    return [...this.recentlyNotified];
  }

  public getRecentlyShortcutted(): Bookmark[] {
    return [...this.recentlyShortcutted];
  }

  public getRecentlyDraggedAndDropped(): Bookmark[] {
    return [...this.recentlyDraggedAndDropped];
  }

  public getRecentlyContextMenued(): Bookmark[] {
    return [...this.recentlyContextMenued];
  }

  public getRecentlyToolbarred(): Bookmark[] {
    return [...this.recentlyToolbarred];
  }

  public getRecentlySidebarred(): Bookmark[] {
    return [...this.recentlySidebarred];
  }

  public getRecentlyBookmarkBarred(): Bookmark[] {
    return [...this.recentlyBookmarkBarred];
  }

  public getRecentlyQuickAdded(): Bookmark[] {
    return [...this.recentlyQuickAdded];
  }

  public getRecentlyQuickEdited(): Bookmark[] {
    return [...this.recentlyQuickEdited];
  }

  public getRecentlyQuickDeleted(): Bookmark[] {
    return [...this.recentlyQuickDeleted];
  }

  public getRecentlyQuickMoved(): Bookmark[] {
    return [...this.recentlyQuickMoved];
  }

  public getRecentlyQuickCopied(): Bookmark[] {
    return [...this.recentlyQuickCopied];
  }

  public getRecentlyQuickPasted(): Bookmark[] {
    return [...this.recentlyQuickPasted];
  }

  public getRecentlyQuickSearched(): Bookmark[] {
    return [...this.recentlyQuickSearched];
  }

  public getRecentlyQuickFiltered(): Bookmark[] {
    return [...this.recentlyQuickFiltered];
  }

  public getRecentlyQuickSorted(): Bookmark[] {
    return [...this.recentlyQuickSorted];
  }

  public getRecentlyQuickGrouped(): Bookmark[] {
    return [...this.recentlyQuickGrouped];
  }

  public getRecentlyQuickStatted(): Bookmark[] {
    return [...this.recentlyQuickStatted];
  }

  public getRecentlyQuickAnalyzed(): Bookmark[] {
    return [...this.recentlyQuickAnalyzed];
  }

  public getRecentlyQuickNotified(): Bookmark[] {
    return [...this.recentlyQuickNotified];
  }

  public getRecentlyQuickShortcutted(): Bookmark[] {
    return [...this.recentlyQuickShortcutted];
  }

  public getRecentlyQuickDraggedAndDropped(): Bookmark[] {
    return [...this.recentlyQuickDraggedAndDropped];
  }

  public getRecentlyQuickContextMenued(): Bookmark[] {
    return [...this.recentlyQuickContextMenued];
  }

  public getRecentlyQuickToolbarred(): Bookmark[] {
    return [...this.recentlyQuickToolbarred];
  }

  public getRecentlyQuickSidebarred(): Bookmark[] {
    return [...this.recentlyQuickSidebarred];
  }

  public getRecentlyQuickBookmarkBarred(): Bookmark[] {
    return [...this.recentlyQuickBookmarkBarred];
  }
}
