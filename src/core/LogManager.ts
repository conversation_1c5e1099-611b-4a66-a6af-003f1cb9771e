import { EventEmitter } from 'events';
import { WriteStream, createWriteStream, promises as fs } from 'fs';
import path from 'path';
import { format } from 'util';

import { app } from 'electron';

export enum LogLevel {
  DEBUG = 'debug',
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  FATAL = 'fatal',
}

export interface LogEntry {
  timestamp: string;
  level: LogLevel;
  category: string;
  message: string;
  data?: any;
  stack?: string;
}

export interface LogConfig {
  level: LogLevel;
  maxFileSize: number;
  maxFiles: number;
  enableConsole: boolean;
  enableFile: boolean;
  enableRemote: boolean;
  categories: {
    [key: string]: boolean;
  };
}

export class LogManager extends EventEmitter {
  private static instance: LogManager;
  private config: LogConfig;
  private logPath: string;
  private currentLogFile: string;
  private writeStream: WriteStream | null;
  private isInitialized: boolean;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.logPath = path.join(app.getPath('userData'), 'logs');
    this.currentLogFile = '';
    this.writeStream = null;
    this.isInitialized = false;
    this.initialize();
  }

  public static getInstance(): LogManager {
    if (!LogManager.instance) {
      LogManager.instance = new LogManager();
    }
    return LogManager.instance;
  }

  private getDefaultConfig(): LogConfig {
    return {
      level: LogLevel.INFO,
      maxFileSize: 10 * 1024 * 1024, // 10MB
      maxFiles: 5,
      enableConsole: true,
      enableFile: true,
      enableRemote: false,
      categories: {
        general: true,
        security: true,
        performance: true,
        network: true,
        storage: true,
        ui: true,
        updates: true,
        errors: true,
        crashes: true,
        analytics: true,
        telemetry: true,
        debugging: true,
      },
    };
  }

  private async initialize(): Promise<void> {
    try {
      await this.setupLogDirectory();
      await this.rotateLogs();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize logging:', error);
      this.emit('initialization-error', error);
    }
  }

  private async setupLogDirectory(): Promise<void> {
    try {
      await fs.mkdir(this.logPath, { recursive: true });
    } catch (error) {
      console.error('Failed to create log directory:', error);
      throw error;
    }
  }

  private async rotateLogs(): Promise<void> {
    try {
      const files = await fs.readdir(this.logPath);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .sort()
        .reverse();

      // Remove old log files if we exceed maxFiles
      while (logFiles.length >= this.config.maxFiles) {
        const oldestFile = logFiles.pop();
        if (oldestFile) {
          await fs.unlink(path.join(this.logPath, oldestFile));
        }
      }

      // Create new log file
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      this.currentLogFile = path.join(this.logPath, `browser-${timestamp}.log`);
      this.writeStream = createWriteStream(this.currentLogFile, { flags: 'a' });
    } catch (error) {
      console.error('Failed to rotate logs:', error);
      throw error;
    }
  }

  private async checkFileSize(): Promise<void> {
    if (!this.writeStream) return;

    try {
      const stats = await fs.stat(this.currentLogFile);
      if (stats.size >= this.config.maxFileSize) {
        this.writeStream.end();
        await this.rotateLogs();
      }
    } catch (error) {
      console.error('Failed to check log file size:', error);
    }
  }

  private formatLogEntry(entry: LogEntry): string {
    const timestamp = new Date().toISOString();
    const level = entry.level.toUpperCase();
    const category = entry.category.toUpperCase();
    let message = `[${timestamp}] [${level}] [${category}] ${entry.message}`;

    if (entry.data) {
      message += `\nData: ${format(entry.data)}`;
    }

    if (entry.stack) {
      message += `\nStack: ${entry.stack}`;
    }

    return `${message}\n`;
  }

  public async log(
    level: LogLevel,
    category: string,
    message: string,
    data?: any,
    error?: Error
  ): Promise<void> {
    if (!this.isInitialized) {
      console.error('LogManager not initialized');
      return;
    }

    if (!this.config.categories[category]) {
      return;
    }

    if (this.getLogLevelValue(level) < this.getLogLevelValue(this.config.level)) {
      return;
    }

    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      stack: error?.stack,
    };

    const formattedMessage = this.formatLogEntry(entry);

    if (this.config.enableConsole) {
      switch (level) {
        case LogLevel.DEBUG:
          console.debug(formattedMessage);
          break;
        case LogLevel.INFO:
          console.info(formattedMessage);
          break;
        case LogLevel.WARN:
          console.warn(formattedMessage);
          break;
        case LogLevel.ERROR:
        case LogLevel.FATAL:
          console.error(formattedMessage);
          break;
      }
    }

    if (this.config.enableFile && this.writeStream) {
      this.writeStream.write(formattedMessage);
      await this.checkFileSize();
    }

    if (this.config.enableRemote) {
      // Implement remote logging here
      this.emit('remote-log', entry);
    }

    this.emit('log', entry);
  }

  private getLogLevelValue(level: LogLevel): number {
    switch (level) {
      case LogLevel.DEBUG:
        return 0;
      case LogLevel.INFO:
        return 1;
      case LogLevel.WARN:
        return 2;
      case LogLevel.ERROR:
        return 3;
      case LogLevel.FATAL:
        return 4;
      default:
        return 0;
    }
  }

  public async setConfig(newConfig: Partial<LogConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    this.emit('config-updated', this.config);
  }

  public getConfig(): LogConfig {
    return { ...this.config };
  }

  public async getLogs(
    level?: LogLevel,
    category?: string,
    startTime?: Date,
    endTime?: Date
  ): Promise<LogEntry[]> {
    try {
      const files = await fs.readdir(this.logPath);
      const logFiles = files
        .filter(file => file.endsWith('.log'))
        .sort()
        .reverse();

      let logs: LogEntry[] = [];

      for (const file of logFiles) {
        const content = await fs.readFile(path.join(this.logPath, file), 'utf-8');
        const entries = content
          .split('\n')
          .filter(line => line.trim())
          .map(line => {
            try {
              return JSON.parse(line);
            } catch {
              return null;
            }
          })
          .filter((entry): entry is LogEntry => entry !== null);

        logs = logs.concat(entries);
      }

      return logs.filter(entry => {
        if (level && entry.level !== level) return false;
        if (category && entry.category !== category) return false;
        if (startTime && new Date(entry.timestamp) < startTime) return false;
        if (endTime && new Date(entry.timestamp) > endTime) return false;
        return true;
      });
    } catch (error) {
      console.error('Failed to get logs:', error);
      return [];
    }
  }

  public async clearLogs(): Promise<void> {
    try {
      const files = await fs.readdir(this.logPath);
      for (const file of files) {
        if (file.endsWith('.log')) {
          await fs.unlink(path.join(this.logPath, file));
        }
      }
      await this.rotateLogs();
      this.emit('logs-cleared');
    } catch (error) {
      console.error('Failed to clear logs:', error);
      throw error;
    }
  }

  public async exportLogs(
    level?: LogLevel,
    category?: string,
    startTime?: Date,
    endTime?: Date
  ): Promise<string> {
    const logs = await this.getLogs(level, category, startTime, endTime);
    return JSON.stringify(logs, null, 2);
  }

  public getInitializationStatus(): boolean {
    return this.isInitialized;
  }
}
