import { createHash } from 'crypto';
import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import * as path from 'path';
import { promisify } from 'util';
import * as zlib from 'zlib';

import { app } from 'electron';

const gzip = promisify(zlib.gzip);
const gunzip = promisify(zlib.gunzip);

export interface CacheConfig {
  maxSize: number;
  maxAge: number;
  strategy: 'lru' | 'fifo' | 'lfu';
  persist: boolean;
  cleanupInterval: number;
  compression: boolean;
}

export interface CacheEntry<T> {
  key: string;
  value: T;
  timestamp: number;
  expires: number;
  size: number;
  hits: number;
  expiresAt?: number;
  type: string;
}

interface CacheEntry {
  key: string;
  value: Buffer;
  metadata: {
    contentType: string;
    size: number;
    compressedSize: number;
    createdAt: number;
    lastAccessed: number;
    expiresAt?: number;
    etag?: string;
    priority: number;
  };
}

interface CacheSettings {
  enabled: boolean;
  maxSize: number;
  maxEntries: number;
  compressionEnabled: boolean;
  compressionThreshold: number;
  cleanupInterval: number;
  defaultTTL: number;
  categories: {
    images: {
      enabled: boolean;
      maxSize: number;
      priority: number;
    };
    documents: {
      enabled: boolean;
      maxSize: number;
      priority: number;
    };
    media: {
      enabled: boolean;
      maxSize: number;
      priority: number;
    };
    api: {
      enabled: boolean;
      maxSize: number;
      priority: number;
    };
  };
}

export class CacheManager extends EventEmitter {
  private static instance: CacheManager;
  private config: CacheConfig;
  private eventEmitter: EventEmitter;
  private cache: Map<string, CacheEntry<any>>;
  private totalSize: number;
  private cleanupInterval: NodeJS.Timeout | null = null;
  private settings: CacheSettings;
  private isInitialized: boolean = false;
  private currentSize: number = 0;

  private constructor() {
    super();
    this.eventEmitter = new EventEmitter();
    this.cache = new Map();
    this.totalSize = 0;

    this.config = {
      maxSize: 100 * 1024 * 1024, // 100MB
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      strategy: 'lru',
      persist: true,
      cleanupInterval: 60 * 60 * 1000, // 1 hour
      compression: true,
    };

    this.settings = {
      enabled: true,
      maxSize: 1024 * 1024 * 1024, // 1GB
      maxEntries: 10000,
      compressionEnabled: true,
      compressionThreshold: 1024, // 1KB
      cleanupInterval: 5 * 60 * 1000, // 5 minutes
      defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
      categories: {
        images: {
          enabled: true,
          maxSize: 100 * 1024 * 1024, // 100MB
          priority: 3,
        },
        documents: {
          enabled: true,
          maxSize: 50 * 1024 * 1024, // 50MB
          priority: 2,
        },
        media: {
          enabled: true,
          maxSize: 200 * 1024 * 1024, // 200MB
          priority: 4,
        },
        api: {
          enabled: true,
          maxSize: 10 * 1024 * 1024, // 10MB
          priority: 1,
        },
      },
    };

    this.loadPersistedCache();
  }

  public static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager();
    }
    return CacheManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadCache();
      if (this.settings.enabled) {
        this.startCleanupInterval();
      }
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize CacheManager:', error);
      throw error;
    }
  }

  // Загрузка кэша из хранилища
  private async loadPersistedCache(): Promise<void> {
    if (!this.config.persist) return;

    try {
      const persisted = localStorage.getItem('novabrowser_cache');
      if (persisted) {
        const data = JSON.parse(persisted);
        this.cache = new Map(Object.entries(data));
        this.calculateTotalSize();
      }
    } catch (error) {
      console.error('Error loading persisted cache:', error);
    }
  }

  // Сохранение кэша в хранилище
  private async persistCache(): Promise<void> {
    if (!this.config.persist) return;

    try {
      const data = Object.fromEntries(this.cache);
      localStorage.setItem('novabrowser_cache', JSON.stringify(data));
    } catch (error) {
      console.error('Error persisting cache:', error);
    }
  }

  // Расчет общего размера кэша
  private calculateTotalSize(): void {
    this.totalSize = Array.from(this.cache.values()).reduce(
      (total, entry) => total + entry.size,
      0
    );
  }

  // Получение значения из кэша
  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    if (Date.now() > entry.expires) {
      this.delete(key);
      return null;
    }

    entry.hits++;
    this.eventEmitter.emit('cacheHit', { key, entry });
    return entry.value;
  }

  // Сохранение значения в кэш
  set<T>(key: string, value: T, options: Partial<CacheConfig> = {}): void {
    const size = this.calculateSize(value);
    const entry: CacheEntry<T> = {
      key,
      value,
      timestamp: Date.now(),
      expires: Date.now() + (options.maxAge || this.config.maxAge),
      size,
      hits: 0,
      type: typeof value,
    };

    // Проверка размера
    if (this.totalSize + size > this.config.maxSize) {
      this.evict();
    }

    this.cache.set(key, entry);
    this.totalSize += size;
    this.eventEmitter.emit('cacheSet', { key, entry });

    if (this.config.persist) {
      this.persistCache();
    }
  }

  // Удаление значения из кэша
  delete(key: string): void {
    const entry = this.cache.get(key);
    if (entry) {
      this.totalSize -= entry.size;
      this.cache.delete(key);
      this.eventEmitter.emit('cacheDelete', { key });
    }
  }

  // Очистка кэша
  clear(): void {
    this.cache.clear();
    this.totalSize = 0;
    this.eventEmitter.emit('cacheClear');
    if (this.config.persist) {
      this.persistCache();
    }
  }

  // Вытеснение старых записей
  private evict(): void {
    switch (this.config.strategy) {
      case 'lru':
        this.evictLRU();
        break;
      case 'fifo':
        this.evictFIFO();
        break;
      case 'lfu':
        this.evictLFU();
        break;
    }
  }

  // Вытеснение по LRU
  private evictLRU(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    this.delete(entries[0][0]);
  }

  // Вытеснение по FIFO
  private evictFIFO(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
    this.delete(entries[0][0]);
  }

  // Вытеснение по LFU
  private evictLFU(): void {
    const entries = Array.from(this.cache.entries());
    entries.sort((a, b) => a[1].hits - b[1].hits);
    this.delete(entries[0][0]);
  }

  // Расчет размера значения
  private calculateSize(value: any): number {
    try {
      const str = JSON.stringify(value);
      return new Blob([str]).size;
    } catch {
      return 0;
    }
  }

  // Получение статистики кэша
  getStats(): {
    size: number;
    count: number;
    hitRate: number;
    missRate: number;
  } {
    const hits = Array.from(this.cache.values()).reduce((total, entry) => total + entry.hits, 0);
    const total = this.cache.size;
    const hitRate = total > 0 ? hits / total : 0;

    return {
      size: this.totalSize,
      count: total,
      hitRate,
      missRate: 1 - hitRate,
    };
  }

  // Подписка на события кэша
  onCacheEvent(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  // Обновление конфигурации
  updateConfig(newConfig: Partial<CacheConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.eventEmitter.emit('configUpdated', this.config);
  }

  private async loadCache(): Promise<void> {
    try {
      const cachePath = path.join(app.getPath('userData'), 'cache.json');
      const data = await fs.readFile(cachePath, 'utf-8');
      const entries = JSON.parse(data);

      for (const entry of entries) {
        this.cache.set(entry.key, entry);
        this.totalSize += entry.size;
      }
    } catch (error) {
      // If cache doesn't exist or is corrupted, start with empty cache
      this.cache.clear();
      this.totalSize = 0;
    }
  }

  private async saveCache(): Promise<void> {
    const cachePath = path.join(app.getPath('userData'), 'cache.json');
    const entries = Array.from(this.cache.values());
    await fs.writeFile(cachePath, JSON.stringify(entries, null, 2));
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private async cleanup(): Promise<void> {
    const now = Date.now();
    let newSize = 0;

    // Remove expired entries
    for (const [key, entry] of this.cache.entries()) {
      if (entry.expiresAt && entry.expiresAt < now) {
        this.cache.delete(key);
      } else {
        newSize += entry.size;
      }
    }

    // If still over max size, remove oldest entries
    if (newSize > this.config.maxSize) {
      const entries = Array.from(this.cache.entries()).sort(
        (a, b) => a[1].timestamp - b[1].timestamp
      );

      for (const [key, entry] of entries) {
        if (newSize <= this.config.maxSize) break;
        this.cache.delete(key);
        newSize -= entry.size;
      }
    }

    this.totalSize = newSize;
    await this.saveCache();
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'cache-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'cache-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  public async set(
    key: string,
    value: Buffer,
    options: {
      contentType: string;
      ttl?: number;
      etag?: string;
      category?: keyof CacheSettings['categories'];
      priority?: number;
    }
  ): Promise<void> {
    if (!this.settings.enabled) return;

    const category = options.category || 'api';
    if (!this.settings.categories[category].enabled) return;

    const entry: CacheEntry = {
      key,
      value,
      metadata: {
        contentType: options.contentType,
        size: value.length,
        compressedSize: value.length,
        createdAt: Date.now(),
        lastAccessed: Date.now(),
        expiresAt: options.ttl ? Date.now() + options.ttl : Date.now() + this.settings.defaultTTL,
        etag: options.etag,
        priority: options.priority || this.settings.categories[category].priority,
      },
    };

    // Compress if enabled and over threshold
    if (this.settings.compressionEnabled && value.length > this.settings.compressionThreshold) {
      const compressed = await gzip(value);
      entry.value = compressed;
      entry.metadata.compressedSize = compressed.length;
    }

    // Check if we need to make space
    if (this.currentSize + entry.metadata.size > this.settings.maxSize) {
      await this.cleanup();
    }

    this.cache.set(key, entry);
    this.currentSize += entry.metadata.size;
    await this.saveCache();
    this.emit('cache-set', key);
  }

  public async get(key: string): Promise<Buffer | null> {
    if (!this.settings.enabled) return null;

    const entry = this.cache.get(key);
    if (!entry) return null;

    // Check if expired
    if (entry.metadata.expiresAt && entry.metadata.expiresAt < Date.now()) {
      this.cache.delete(key);
      this.currentSize -= entry.metadata.size;
      await this.saveCache();
      return null;
    }

    // Update last accessed time
    entry.metadata.lastAccessed = Date.now();
    await this.saveCache();

    // Decompress if needed
    if (entry.metadata.compressedSize < entry.metadata.size) {
      return gunzip(entry.value);
    }

    return entry.value;
  }

  public async delete(key: string): Promise<void> {
    const entry = this.cache.get(key);
    if (entry) {
      this.currentSize -= entry.metadata.size;
      this.cache.delete(key);
      await this.saveCache();
      this.emit('cache-deleted', key);
    }
  }

  public async clear(): Promise<void> {
    this.cache.clear();
    this.currentSize = 0;
    await this.saveCache();
    this.emit('cache-cleared');
  }

  public getStats(): {
    size: number;
    entries: number;
    categories: Record<string, { size: number; entries: number }>;
  } {
    const stats = {
      size: this.currentSize,
      entries: this.cache.size,
      categories: {} as Record<string, { size: number; entries: number }>,
    };

    for (const category of Object.keys(this.settings.categories)) {
      stats.categories[category] = { size: 0, entries: 0 };
    }

    for (const entry of this.cache.values()) {
      const category =
        Object.keys(this.settings.categories).find(cat =>
          entry.metadata.contentType.startsWith(cat)
        ) || 'api';

      stats.categories[category].size += entry.metadata.size;
      stats.categories[category].entries++;
    }

    return stats;
  }

  public getSettings(): CacheSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<CacheSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();

    if (this.settings.enabled) {
      this.startCleanupInterval();
    } else if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }

  public cleanup(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}

// Создание синглтона
export const cacheManager = CacheManager.getInstance();
