import { promises as fs } from 'fs';
import * as path from 'path';

import { app } from 'electron';
import { v4 as uuidv4 } from 'uuid';

interface Session {
  id: string;
  name: string;
  tabs: Tab[];
  windows: Window[];
  createdAt: number;
  lastModified: number;
  isPinned: boolean;
  isPrivate: boolean;
  metadata: {
    deviceInfo: any;
    osInfo: any;
    browserInfo: any;
  };
}

interface Tab {
  id: string;
  url: string;
  title: string;
  favicon?: string;
  isActive: boolean;
  isPinned: boolean;
  lastAccessed: number;
  scrollPosition: number;
  formData?: any;
}

interface Window {
  id: string;
  bounds: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  isMaximized: boolean;
  isFullscreen: boolean;
  tabs: string[]; // Tab IDs
}

interface SessionSettings {
  autoSave: boolean;
  saveInterval: number;
  maxSessions: number;
  maxSessionAge: number;
  backupEnabled: boolean;
  backupInterval: number;
  maxBackups: number;
}

export class SessionManager {
  private static instance: SessionManager;
  private sessions: Map<string, Session>;
  private settings: SessionSettings;
  private saveInterval: NodeJS.Timeout | null = null;
  private backupInterval: NodeJS.Timeout | null = null;
  private currentSession: Session | null = null;

  private constructor() {
    this.sessions = new Map();
    this.settings = {
      autoSave: true,
      saveInterval: 5 * 60 * 1000, // 5 minutes
      maxSessions: 50,
      maxSessionAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      backupEnabled: true,
      backupInterval: 24 * 60 * 60 * 1000, // 24 hours
      maxBackups: 5,
    };
  }

  public static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager();
    }
    return SessionManager.instance;
  }

  public async initialize(): Promise<void> {
    await this.loadSessions();
    await this.loadSettings();
    if (this.settings.autoSave) {
      this.startSaveInterval();
    }
    if (this.settings.backupEnabled) {
      this.startBackupInterval();
    }
  }

  private async loadSessions(): Promise<void> {
    try {
      const sessionsPath = path.join(app.getPath('userData'), 'sessions');
      await fs.mkdir(sessionsPath, { recursive: true });

      const entries = await fs.readdir(sessionsPath, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          try {
            const sessionPath = path.join(sessionsPath, entry.name);
            const sessionData = await fs.readFile(path.join(sessionPath, 'session.json'), 'utf-8');
            const session = JSON.parse(sessionData);
            this.sessions.set(session.id, session);
          } catch (error) {
            console.error(`Failed to load session ${entry.name}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'session-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      // If settings don't exist, use defaults
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'session-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private startSaveInterval(): void {
    this.saveInterval = setInterval(() => {
      this.saveCurrentSession();
    }, this.settings.saveInterval);
  }

  private startBackupInterval(): void {
    this.backupInterval = setInterval(() => {
      this.createBackup();
    }, this.settings.backupInterval);
  }

  private async saveCurrentSession(): Promise<void> {
    if (this.currentSession) {
      await this.saveSession(this.currentSession);
    }
  }

  private async saveSession(session: Session): Promise<void> {
    const sessionPath = path.join(app.getPath('userData'), 'sessions', session.id);
    await fs.mkdir(sessionPath, { recursive: true });
    await fs.writeFile(path.join(sessionPath, 'session.json'), JSON.stringify(session, null, 2));
  }

  private async createBackup(): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = path.join(app.getPath('userData'), 'session-backups', `backup-${timestamp}`);

    await fs.mkdir(backupPath, { recursive: true });

    for (const session of this.sessions.values()) {
      const sessionBackupPath = path.join(backupPath, session.id);
      await fs.mkdir(sessionBackupPath, { recursive: true });
      await this.copySession(session.id, sessionBackupPath);
    }

    await this.cleanupOldBackups();
  }

  private async cleanupOldBackups(): Promise<void> {
    const backupDir = path.join(app.getPath('userData'), 'session-backups');
    const backups = await fs.readdir(backupDir);

    if (backups.length > this.settings.maxBackups) {
      const sortedBackups = backups
        .map(backup => ({
          name: backup,
          time: fs.stat(path.join(backupDir, backup)).then(stat => stat.mtime.getTime()),
        }))
        .sort((a, b) => b.time - a.time);

      for (let i = this.settings.maxBackups; i < sortedBackups.length; i++) {
        await fs.rm(path.join(backupDir, sortedBackups[i].name), { recursive: true, force: true });
      }
    }
  }

  public async createSession(options: Partial<Session>): Promise<Session> {
    if (this.sessions.size >= this.settings.maxSessions) {
      await this.cleanupOldSessions();
    }

    const session: Session = {
      id: options.id || uuidv4(),
      name: options.name || 'New Session',
      tabs: options.tabs || [],
      windows: options.windows || [],
      createdAt: Date.now(),
      lastModified: Date.now(),
      isPinned: options.isPinned || false,
      isPrivate: options.isPrivate || false,
      metadata: options.metadata || {
        deviceInfo: {},
        osInfo: {},
        browserInfo: {},
      },
    };

    this.sessions.set(session.id, session);
    await this.saveSession(session);
    return session;
  }

  private async cleanupOldSessions(): Promise<void> {
    const now = Date.now();
    const sessions = Array.from(this.sessions.values())
      .filter(session => !session.isPinned)
      .sort((a, b) => b.lastModified - a.lastModified);

    for (let i = this.settings.maxSessions - 1; i < sessions.length; i++) {
      const session = sessions[i];
      if (now - session.lastModified > this.settings.maxSessionAge) {
        await this.deleteSession(session.id);
      }
    }
  }

  private async copySession(sessionId: string, targetPath: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const sessionPath = path.join(app.getPath('userData'), 'sessions', sessionId);
    await this.copyDirectory(sessionPath, targetPath);
  }

  private async copyDirectory(src: string, dest: string): Promise<void> {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  public async switchSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    // Save current session if exists
    if (this.currentSession) {
      await this.saveSession(this.currentSession);
    }

    // Load new session
    this.currentSession = session;
    session.lastModified = Date.now();

    // Emit event for session switch
    this.emit('session-switched', session);
  }

  public async updateSession(sessionId: string, updates: Partial<Session>): Promise<Session> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error(`Session not found: ${sessionId}`);
    }

    const updatedSession = { ...session, ...updates, lastModified: Date.now() };
    this.sessions.set(sessionId, updatedSession);
    await this.saveSession(updatedSession);

    return updatedSession;
  }

  public async deleteSession(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (!session) return;

    const sessionPath = path.join(app.getPath('userData'), 'sessions', sessionId);
    await fs.rm(sessionPath, { recursive: true, force: true });
    this.sessions.delete(sessionId);

    if (this.currentSession?.id === sessionId) {
      this.currentSession = null;
    }
  }

  public getSession(sessionId: string): Session | undefined {
    return this.sessions.get(sessionId);
  }

  public getAllSessions(): Session[] {
    return Array.from(this.sessions.values());
  }

  public getCurrentSession(): Session | null {
    return this.currentSession;
  }

  public getSettings(): SessionSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<SessionSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();

    if (this.settings.autoSave) {
      this.startSaveInterval();
    } else if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }

    if (this.settings.backupEnabled) {
      this.startBackupInterval();
    } else if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }
  }

  public cleanup(): void {
    if (this.saveInterval) {
      clearInterval(this.saveInterval);
    }
    if (this.backupInterval) {
      clearInterval(this.backupInterval);
    }
  }
}
