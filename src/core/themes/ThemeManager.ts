import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import * as path from 'path';

import * as autoprefixer from 'autoprefixer';
import * as cssnano from 'cssnano';
import { app } from 'electron';
import * as less from 'less';
import * as postcss from 'postcss';
import * as sass from 'sass';

interface Theme {
  id: string;
  name: string;
  description: string;
  author: string;
  version: string;
  type: 'light' | 'dark' | 'custom';
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    error: string;
    warning: string;
    success: string;
    info: string;
    text: {
      primary: string;
      secondary: string;
      disabled: string;
    };
  };
  typography: {
    fontFamily: string;
    fontSize: {
      base: string;
      small: string;
      large: string;
      h1: string;
      h2: string;
      h3: string;
    };
    fontWeight: {
      light: number;
      regular: number;
      medium: number;
      bold: number;
    };
    lineHeight: {
      tight: number;
      normal: number;
      relaxed: number;
    };
  };
  spacing: {
    unit: number;
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    full: string;
  };
  shadows: {
    none: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  transitions: {
    fast: string;
    normal: string;
    slow: string;
  };
  breakpoints: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  components: {
    button: {
      borderRadius: string;
      padding: string;
      fontSize: string;
    };
    input: {
      borderRadius: string;
      padding: string;
      fontSize: string;
    };
    card: {
      borderRadius: string;
      padding: string;
      shadow: string;
    };
  };
  metadata: {
    createdAt: number;
    updatedAt: number;
    isSystem: boolean;
    isCustom: boolean;
    isEnabled: boolean;
    compatibility: {
      minVersion: string;
      maxVersion: string;
    };
  };
}

interface ThemeSettings {
  currentTheme: string;
  autoSwitch: boolean;
  schedule: {
    light: string;
    dark: string;
  };
  customThemes: string[];
  systemThemes: string[];
  previewEnabled: boolean;
  animationEnabled: boolean;
  highContrast: boolean;
  reducedMotion: boolean;
  colorBlindness: {
    enabled: boolean;
    type: 'protanopia' | 'deuteranopia' | 'tritanopia';
  };
}

export class ThemeManager extends EventEmitter {
  private static instance: ThemeManager;
  private themes: Map<string, Theme>;
  private settings: ThemeSettings;
  private isInitialized: boolean = false;
  private styleSheet: HTMLStyleElement | null = null;

  private constructor() {
    super();
    this.themes = new Map();
    this.settings = {
      currentTheme: 'default',
      autoSwitch: false,
      schedule: {
        light: '08:00',
        dark: '20:00',
      },
      customThemes: [],
      systemThemes: [],
      previewEnabled: true,
      animationEnabled: true,
      highContrast: false,
      reducedMotion: false,
      colorBlindness: {
        enabled: false,
        type: 'protanopia',
      },
    };
  }

  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadThemes();
      await this.setupStyleSheet();
      await this.applyTheme(this.settings.currentTheme);
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize ThemeManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'theme-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'theme-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async loadThemes(): Promise<void> {
    try {
      const themesPath = path.join(app.getPath('userData'), 'themes');
      await fs.mkdir(themesPath, { recursive: true });

      const entries = await fs.readdir(themesPath, { withFileTypes: true });

      for (const entry of entries) {
        if (entry.isDirectory()) {
          try {
            const themePath = path.join(themesPath, entry.name);
            const manifestPath = path.join(themePath, 'theme.json');
            const manifestData = await fs.readFile(manifestPath, 'utf-8');
            const theme = JSON.parse(manifestData);
            this.themes.set(theme.id, theme);
          } catch (error) {
            console.error(`Failed to load theme ${entry.name}:`, error);
          }
        }
      }
    } catch (error) {
      console.error('Failed to load themes:', error);
    }
  }

  private async setupStyleSheet(): Promise<void> {
    this.styleSheet = document.createElement('style');
    this.styleSheet.id = 'theme-styles';
    document.head.appendChild(this.styleSheet);
  }

  public async applyTheme(themeId: string): Promise<void> {
    const theme = this.themes.get(themeId);
    if (!theme) {
      throw new Error(`Theme not found: ${themeId}`);
    }

    try {
      const css = await this.generateCSS(theme);
      if (this.styleSheet) {
        this.styleSheet.textContent = css;
      }

      this.settings.currentTheme = themeId;
      await this.saveSettings();
      this.emit('theme-applied', theme);
    } catch (error) {
      console.error(`Failed to apply theme ${themeId}:`, error);
      throw error;
    }
  }

  private async generateCSS(theme: Theme): Promise<string> {
    const css = `
      :root {
        /* Colors */
        --color-primary: ${theme.colors.primary};
        --color-secondary: ${theme.colors.secondary};
        --color-accent: ${theme.colors.accent};
        --color-background: ${theme.colors.background};
        --color-surface: ${theme.colors.surface};
        --color-error: ${theme.colors.error};
        --color-warning: ${theme.colors.warning};
        --color-success: ${theme.colors.success};
        --color-info: ${theme.colors.info};
        --color-text-primary: ${theme.colors.text.primary};
        --color-text-secondary: ${theme.colors.text.secondary};
        --color-text-disabled: ${theme.colors.text.disabled};

        /* Typography */
        --font-family: ${theme.typography.fontFamily};
        --font-size-base: ${theme.typography.fontSize.base};
        --font-size-small: ${theme.typography.fontSize.small};
        --font-size-large: ${theme.typography.fontSize.large};
        --font-size-h1: ${theme.typography.fontSize.h1};
        --font-size-h2: ${theme.typography.fontSize.h2};
        --font-size-h3: ${theme.typography.fontSize.h3};
        --font-weight-light: ${theme.typography.fontWeight.light};
        --font-weight-regular: ${theme.typography.fontWeight.regular};
        --font-weight-medium: ${theme.typography.fontWeight.medium};
        --font-weight-bold: ${theme.typography.fontWeight.bold};
        --line-height-tight: ${theme.typography.lineHeight.tight};
        --line-height-normal: ${theme.typography.lineHeight.normal};
        --line-height-relaxed: ${theme.typography.lineHeight.relaxed};

        /* Spacing */
        --spacing-unit: ${theme.spacing.unit}px;
        --spacing-xs: ${theme.spacing.xs};
        --spacing-sm: ${theme.spacing.sm};
        --spacing-md: ${theme.spacing.md};
        --spacing-lg: ${theme.spacing.lg};
        --spacing-xl: ${theme.spacing.xl};

        /* Border Radius */
        --border-radius-none: ${theme.borderRadius.none};
        --border-radius-sm: ${theme.borderRadius.sm};
        --border-radius-md: ${theme.borderRadius.md};
        --border-radius-lg: ${theme.borderRadius.lg};
        --border-radius-full: ${theme.borderRadius.full};

        /* Shadows */
        --shadow-none: ${theme.shadows.none};
        --shadow-sm: ${theme.shadows.sm};
        --shadow-md: ${theme.shadows.md};
        --shadow-lg: ${theme.shadows.lg};
        --shadow-xl: ${theme.shadows.xl};

        /* Transitions */
        --transition-fast: ${theme.transitions.fast};
        --transition-normal: ${theme.transitions.normal};
        --transition-slow: ${theme.transitions.slow};

        /* Breakpoints */
        --breakpoint-xs: ${theme.breakpoints.xs};
        --breakpoint-sm: ${theme.breakpoints.sm};
        --breakpoint-md: ${theme.breakpoints.md};
        --breakpoint-lg: ${theme.breakpoints.lg};
        --breakpoint-xl: ${theme.breakpoints.xl};
      }

      /* Component Styles */
      .button {
        border-radius: ${theme.components.button.borderRadius};
        padding: ${theme.components.button.padding};
        font-size: ${theme.components.button.fontSize};
      }

      .input {
        border-radius: ${theme.components.input.borderRadius};
        padding: ${theme.components.input.padding};
        font-size: ${theme.components.input.fontSize};
      }

      .card {
        border-radius: ${theme.components.card.borderRadius};
        padding: ${theme.components.card.padding};
        box-shadow: ${theme.components.card.shadow};
      }
    `;

    // Process CSS with PostCSS
    const result = await postcss([autoprefixer, cssnano]).process(css, { from: undefined });

    return result.css;
  }

  public async createTheme(theme: Omit<Theme, 'id' | 'metadata'>): Promise<Theme> {
    const newTheme: Theme = {
      ...theme,
      id: Math.random().toString(36).substr(2, 9),
      metadata: {
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isSystem: false,
        isCustom: true,
        isEnabled: true,
        compatibility: {
          minVersion: '1.0.0',
          maxVersion: '2.0.0',
        },
      },
    };

    const themePath = path.join(app.getPath('userData'), 'themes', newTheme.id);
    await fs.mkdir(themePath, { recursive: true });
    await fs.writeFile(path.join(themePath, 'theme.json'), JSON.stringify(newTheme, null, 2));

    this.themes.set(newTheme.id, newTheme);
    this.settings.customThemes.push(newTheme.id);
    await this.saveSettings();
    this.emit('theme-created', newTheme);

    return newTheme;
  }

  public async updateTheme(themeId: string, updates: Partial<Theme>): Promise<Theme> {
    const theme = this.themes.get(themeId);
    if (!theme) {
      throw new Error(`Theme not found: ${themeId}`);
    }

    const updatedTheme = {
      ...theme,
      ...updates,
      metadata: {
        ...theme.metadata,
        updatedAt: Date.now(),
      },
    };

    const themePath = path.join(app.getPath('userData'), 'themes', themeId);
    await fs.writeFile(path.join(themePath, 'theme.json'), JSON.stringify(updatedTheme, null, 2));

    this.themes.set(themeId, updatedTheme);
    await this.saveSettings();
    this.emit('theme-updated', updatedTheme);

    return updatedTheme;
  }

  public async deleteTheme(themeId: string): Promise<void> {
    const theme = this.themes.get(themeId);
    if (!theme) {
      throw new Error(`Theme not found: ${themeId}`);
    }

    if (theme.metadata.isSystem) {
      throw new Error('Cannot delete system theme');
    }

    const themePath = path.join(app.getPath('userData'), 'themes', themeId);
    await fs.rm(themePath, { recursive: true, force: true });

    this.themes.delete(themeId);
    this.settings.customThemes = this.settings.customThemes.filter(id => id !== themeId);
    await this.saveSettings();
    this.emit('theme-deleted', theme);
  }

  public getTheme(themeId: string): Theme | undefined {
    return this.themes.get(themeId);
  }

  public getAllThemes(): Theme[] {
    return Array.from(this.themes.values());
  }

  public getCustomThemes(): Theme[] {
    return Array.from(this.themes.values()).filter(theme => theme.metadata.isCustom);
  }

  public getSystemThemes(): Theme[] {
    return Array.from(this.themes.values()).filter(theme => theme.metadata.isSystem);
  }

  public getSettings(): ThemeSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<ThemeSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();

    if (settings.currentTheme) {
      await this.applyTheme(settings.currentTheme);
    }
  }

  public cleanup(): void {
    if (this.styleSheet) {
      this.styleSheet.remove();
    }
  }
}
