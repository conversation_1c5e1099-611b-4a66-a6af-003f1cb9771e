import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';

import { BrowserWindow, app, dialog } from 'electron';
import {
  UpdateInfo as ElectronUpdateInfo,
  UpdateDownloadedEvent,
  autoUpdater,
} from 'electron-updater';

export interface UpdateInfo {
  version: string;
  releaseDate: string;
  releaseNotes: string;
  downloadUrl: string;
  size: number;
  hash: string;
  isPrerelease: boolean;
  isMandatory: boolean;
}

export interface UpdateProgress {
  bytesPerSecond: number;
  percent: number;
  total: number;
  transferred: number;
}

interface UpdateManagerConfig {
  autoCheck: boolean;
  autoDownload: boolean;
  autoInstall: boolean;
  checkInterval: number;
  channel: string;
  allowPrerelease: boolean;
}

export class UpdateManager extends EventEmitter {
  private static instance: UpdateManager;
  private isChecking: boolean;
  private isDownloading: boolean;
  private updateInfo: UpdateInfo | null;
  private progress: UpdateProgress | null;
  private config: UpdateManagerConfig;
  private isUpdateDownloaded: boolean;

  private constructor() {
    super();
    this.isChecking = false;
    this.isDownloading = false;
    this.updateInfo = null;
    this.progress = null;
    this.config = this.getDefaultConfig();
    this.isUpdateDownloaded = false;
    this.initializeUpdater();
  }

  public static getInstance(): UpdateManager {
    if (!UpdateManager.instance) {
      UpdateManager.instance = new UpdateManager();
    }
    return UpdateManager.instance;
  }

  private getDefaultConfig(): UpdateManagerConfig {
    return {
      autoCheck: true,
      autoDownload: true,
      autoInstall: false,
      checkInterval: 1000 * 60 * 60 * 24, // 24 hours
      channel: 'latest',
      allowPrerelease: false,
    };
  }

  private async initializeUpdater(): Promise<void> {
    try {
      await this.setupAutoUpdater();
      await this.loadConfig();
      this.setupUpdateEvents();
      if (this.config.autoCheck) {
        this.startAutoCheck();
      }
    } catch (error) {
      console.error('Update initialization failed:', error);
      this.emit('initialization-error', error);
    }
  }

  private async setupAutoUpdater(): Promise<void> {
    autoUpdater.autoDownload = this.config.autoDownload;
    autoUpdater.autoInstallOnAppQuit = this.config.autoInstall;
    autoUpdater.allowPrerelease = this.config.allowPrerelease;
    autoUpdater.channel = this.config.channel;

    // Configure update server
    autoUpdater.setFeedURL({
      provider: 'github',
      owner: 'your-username',
      repo: 'your-repo',
      token: process.env.GITHUB_TOKEN,
    });
  }

  private async loadConfig(): Promise<void> {
    try {
      const configPath = path.join(app.getPath('userData'), 'update-config.json');
      const data = await fs.readFile(configPath, 'utf-8');
      this.config = { ...this.config, ...JSON.parse(data) };
    } catch (error) {
      console.error('Failed to load update config:', error);
    }
  }

  private setupUpdateEvents(): void {
    autoUpdater.on('checking-for-update', () => {
      this.isChecking = true;
      this.emit('checking');
    });

    autoUpdater.on('update-available', (info: ElectronUpdateInfo) => {
      this.isChecking = false;
      this.updateInfo = {
        version: info.version,
        releaseDate: info.releaseDate,
        releaseNotes: info.releaseNotes?.toString() || '',
        downloadUrl: info.files?.[0]?.url || '',
        size: info.files?.[0]?.size || 0,
        hash: info.files?.[0]?.sha512 || '',
        isPrerelease: false,
        isMandatory: false,
      };
      this.emit('update-available', this.updateInfo);
    });

    autoUpdater.on('update-not-available', () => {
      this.isChecking = false;
      this.updateInfo = null;
      this.emit('update-not-available');
    });

    autoUpdater.on('error', error => {
      this.isChecking = false;
      this.isDownloading = false;
      this.emit('error', error);
    });

    autoUpdater.on('download-progress', progress => {
      this.progress = {
        bytesPerSecond: progress.bytesPerSecond,
        percent: progress.percent,
        total: progress.total,
        transferred: progress.transferred,
      };
      this.emit('download-progress', this.progress);
    });

    autoUpdater.on('update-downloaded', (info: UpdateDownloadedEvent) => {
      this.isDownloading = false;
      this.progress = null;
      this.isUpdateDownloaded = true;
      this.emit('update-downloaded', info);
      this.showUpdateReadyDialog();
    });
  }

  private startAutoCheck(): void {
    setInterval(() => {
      this.checkForUpdates();
    }, this.config.checkInterval);
  }

  private async showUpdateReadyDialog(): Promise<void> {
    const dialogOpts = {
      type: 'info' as const,
      buttons: ['Restart', 'Later'],
      title: 'Application Update',
      message: 'A new version has been downloaded. Restart the application to apply the updates.',
      detail: this.updateInfo?.releaseNotes,
    };

    const { response } = await dialog.showMessageBox(dialogOpts);
    if (response === 0) {
      autoUpdater.quitAndInstall();
    }
  }

  public async checkForUpdates(): Promise<void> {
    if (this.isChecking) return;

    try {
      await autoUpdater.checkForUpdates();
    } catch (error) {
      console.error('Failed to check for updates:', error);
      this.emit('check-error', error);
    }
  }

  public async downloadUpdate(): Promise<void> {
    if (!this.updateInfo || this.isDownloading) return;

    try {
      this.isDownloading = true;
      await autoUpdater.downloadUpdate();
    } catch (error) {
      console.error('Failed to download update:', error);
      this.emit('download-error', error);
    }
  }

  public async installUpdate(): Promise<void> {
    if (!this.updateInfo) return;

    try {
      await autoUpdater.quitAndInstall();
    } catch (error) {
      console.error('Failed to install update:', error);
      this.emit('install-error', error);
    }
  }

  public getUpdateConfig(): UpdateManagerConfig {
    return { ...this.config };
  }

  public async updateConfig(newConfig: Partial<UpdateManagerConfig>): Promise<void> {
    this.config = { ...this.config, ...newConfig };
    await this.saveConfig();
    this.emit('config-updated', this.config);
  }

  private async saveConfig(): Promise<void> {
    try {
      const configPath = path.join(app.getPath('userData'), 'update-config.json');
      await fs.writeFile(configPath, JSON.stringify(this.config, null, 2));
    } catch (error) {
      console.error('Failed to save update config:', error);
    }
  }

  public getUpdateInfo(): UpdateInfo | null {
    return this.updateInfo ? { ...this.updateInfo } : null;
  }

  public getProgress(): UpdateProgress | null {
    return this.progress ? { ...this.progress } : null;
  }

  public isUpdateAvailable(): boolean {
    return this.updateInfo !== null;
  }

  public hasUpdateDownloaded(): boolean {
    return this.isUpdateDownloaded;
  }

  public async exportUpdateConfig(): Promise<string> {
    return JSON.stringify(this.config, null, 2);
  }

  public async importUpdateConfig(configData: string): Promise<void> {
    const config = JSON.parse(configData);
    await this.updateConfig(config);
  }
}
