import * as fs from 'fs/promises';
import * as path from 'path';

import { <PERSON>rowserWindow, app, ipcMain } from 'electron';

import { APP_CONFIG } from '../config/app.config';
import { SecurityManager } from '../security/SecurityManager';

import { ErrorManager } from './ErrorManager';

interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  permissions: string[];
  enabled: boolean;
  path: string;
  manifest: any;
}

interface ExtensionContext {
  extension: Extension;
  window: BrowserWindow;
}

export class ExtensionManager {
  private static instance: ExtensionManager;
  private extensions: Map<string, Extension> = new Map();
  private extensionContexts: Map<string, ExtensionContext[]> = new Map();
  private isInitialized: boolean = false;

  private constructor() {
    this.initialize();
  }

  public static getInstance(): ExtensionManager {
    if (!ExtensionManager.instance) {
      ExtensionManager.instance = new ExtensionManager();
    }
    return ExtensionManager.instance;
  }

  private async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Create extensions directory if it doesn't exist
      const extensionsDir = path.join(app.getPath('userData'), 'extensions');
      await fs.mkdir(extensionsDir, { recursive: true });

      // Load installed extensions
      await this.loadExtensions();

      // Set up IPC handlers
      this.setupIpcHandlers();

      this.isInitialized = true;
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'initialize'
      );
    }
  }

  private async loadExtensions(): Promise<void> {
    const extensionsDir = path.join(app.getPath('userData'), 'extensions');
    const entries = await fs.readdir(extensionsDir, { withFileTypes: true });

    for (const entry of entries) {
      if (entry.isDirectory()) {
        try {
          const manifestPath = path.join(extensionsDir, entry.name, 'manifest.json');
          const manifestContent = await fs.readFile(manifestPath, 'utf-8');
          const manifest = JSON.parse(manifestContent);

          const extension: Extension = {
            id: entry.name,
            name: manifest.name,
            version: manifest.version,
            description: manifest.description,
            author: manifest.author,
            permissions: manifest.permissions || [],
            enabled: true,
            path: path.join(extensionsDir, entry.name),
            manifest,
          };

          this.extensions.set(extension.id, extension);
        } catch (error: unknown) {
          ErrorManager.getInstance().handleError(
            error instanceof Error ? error : new Error(String(error)),
            undefined,
            'ExtensionManager',
            'loadExtensions'
          );
        }
      }
    }
  }

  private setupIpcHandlers(): void {
    ipcMain.handle('extension:getAll', () => {
      return Array.from(this.extensions.values());
    });

    ipcMain.handle('extension:get', (_, id: string) => {
      return this.extensions.get(id);
    });

    ipcMain.handle('extension:install', async (_, extensionPath: string) => {
      return await this.installExtension(extensionPath);
    });

    ipcMain.handle('extension:uninstall', async (_, id: string) => {
      return await this.uninstallExtension(id);
    });

    ipcMain.handle('extension:enable', async (_, id: string) => {
      return await this.enableExtension(id);
    });

    ipcMain.handle('extension:disable', async (_, id: string) => {
      return await this.disableExtension(id);
    });
  }

  public async installExtension(extensionPath: string): Promise<Extension> {
    try {
      // Validate extension
      const manifestPath = path.join(extensionPath, 'manifest.json');
      const manifestContent = await fs.readFile(manifestPath, 'utf-8');
      const manifest = JSON.parse(manifestContent);

      // Check permissions
      if (!this.validatePermissions(manifest.permissions || [])) {
        throw new Error('Extension requires invalid permissions');
      }

      // Copy extension to extensions directory
      const extensionsDir = path.join(app.getPath('userData'), 'extensions');
      const extensionId = manifest.name.toLowerCase().replace(/[^a-z0-9]/g, '-');
      const targetPath = path.join(extensionsDir, extensionId);

      await fs.mkdir(targetPath, { recursive: true });
      await this.copyDirectory(extensionPath, targetPath);

      const extension: Extension = {
        id: extensionId,
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: manifest.author,
        permissions: manifest.permissions || [],
        enabled: true,
        path: targetPath,
        manifest,
      };

      this.extensions.set(extension.id, extension);
      return extension;
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'installExtension'
      );
      throw error;
    }
  }

  public async uninstallExtension(id: string): Promise<void> {
    try {
      const extension = this.extensions.get(id);
      if (!extension) {
        throw new Error(`Extension ${id} not found`);
      }

      // Remove extension files
      await fs.rm(extension.path, { recursive: true, force: true });
      this.extensions.delete(id);
      this.extensionContexts.delete(id);
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'uninstallExtension'
      );
      throw error;
    }
  }

  public async enableExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (!extension) {
      throw new Error(`Extension ${id} not found`);
    }

    extension.enabled = true;
    await this.loadExtension(extension);
  }

  public async disableExtension(id: string): Promise<void> {
    const extension = this.extensions.get(id);
    if (!extension) {
      throw new Error(`Extension ${id} not found`);
    }

    extension.enabled = false;
    await this.unloadExtension(extension);
  }

  private async loadExtension(extension: Extension): Promise<void> {
    try {
      // Load extension scripts
      const scripts = extension.manifest.scripts || [];
      for (const script of scripts) {
        const scriptPath = path.join(extension.path, script);
        await this.loadScript(scriptPath);
      }

      // Load extension styles
      const styles = extension.manifest.styles || [];
      for (const style of styles) {
        const stylePath = path.join(extension.path, style);
        await this.loadStyle(stylePath);
      }
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'loadExtension'
      );
    }
  }

  private async unloadExtension(extension: Extension): Promise<void> {
    try {
      // Unload extension scripts
      const scripts = extension.manifest.scripts || [];
      for (const script of scripts) {
        const scriptPath = path.join(extension.path, script);
        await this.unloadScript(scriptPath);
      }

      // Unload extension styles
      const styles = extension.manifest.styles || [];
      for (const style of styles) {
        const stylePath = path.join(extension.path, style);
        await this.unloadStyle(stylePath);
      }
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'unloadExtension'
      );
    }
  }

  private validatePermissions(permissions: string[]): boolean {
    // Fallback to a default set of allowed permissions if not present in config
    const allowedPermissions = Array.isArray((APP_CONFIG.extensions as any).allowedPermissions)
      ? (APP_CONFIG.extensions as any).allowedPermissions
      : ['tabs', 'storage', 'cookies', 'webRequest', 'notifications'];
    return permissions.every(permission => allowedPermissions.includes(permission));
  }

  private async copyDirectory(src: string, dest: string): Promise<void> {
    await fs.mkdir(dest, { recursive: true });
    const entries = await fs.readdir(src, { withFileTypes: true });

    for (const entry of entries) {
      const srcPath = path.join(src, entry.name);
      const destPath = path.join(dest, entry.name);

      if (entry.isDirectory()) {
        await this.copyDirectory(srcPath, destPath);
      } else {
        await fs.copyFile(srcPath, destPath);
      }
    }
  }

  private async loadScript(scriptPath: string): Promise<void> {
    // Implement script loading logic
  }

  private async unloadScript(scriptPath: string): Promise<void> {
    // Implement script unloading logic
  }

  private async loadStyle(stylePath: string): Promise<void> {
    // Implement style loading logic
  }

  private async unloadStyle(stylePath: string): Promise<void> {
    // Implement style unloading logic
  }

  public getExtension(id: string): Extension | undefined {
    return this.extensions.get(id);
  }

  public getAllExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  public getEnabledExtensions(): Extension[] {
    return Array.from(this.extensions.values()).filter(ext => ext.enabled);
  }

  public async injectExtension(extension: Extension, window: BrowserWindow): Promise<void> {
    try {
      const context: ExtensionContext = { extension, window };
      const contexts = this.extensionContexts.get(extension.id) || [];
      contexts.push(context);
      this.extensionContexts.set(extension.id, contexts);

      // Inject extension scripts and styles
      await this.loadExtension(extension);
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'injectExtension'
      );
    }
  }

  public async removeExtension(extension: Extension, window: BrowserWindow): Promise<void> {
    try {
      const contexts = this.extensionContexts.get(extension.id) || [];
      const index = contexts.findIndex(ctx => ctx.window === window);
      if (index !== -1) {
        contexts.splice(index, 1);
        if (contexts.length === 0) {
          this.extensionContexts.delete(extension.id);
        } else {
          this.extensionContexts.set(extension.id, contexts);
        }
      }

      // Remove extension scripts and styles
      await this.unloadExtension(extension);
    } catch (error: unknown) {
      ErrorManager.getInstance().handleError(
        error instanceof Error ? error : new Error(String(error)),
        undefined,
        'ExtensionManager',
        'removeExtension'
      );
    }
  }
}
