import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';

import { BrowserWindow, app } from 'electron';

import { configManager } from './ConfigurationManager';
import { logger } from './EnhancedLogger';

export interface ThemeColors {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  surface: string;
  error: string;
  warning: string;
  info: string;
  success: string;
  text: {
    primary: string;
    secondary: string;
    disabled: string;
  };
  divider: string;
  border: string;
  shadow: string;
}

export interface ThemeTypography {
  fontFamily: string;
  fontSize: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  fontWeight: {
    light: number;
    regular: number;
    medium: number;
    bold: number;
  };
  lineHeight: {
    tight: number;
    normal: number;
    relaxed: number;
  };
  letterSpacing: {
    tighter: string;
    tight: string;
    normal: string;
    wide: string;
    wider: string;
  };
}

export interface ThemeSpacing {
  xs: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

export interface ThemeBorderRadius {
  none: string;
  sm: string;
  md: string;
  lg: string;
  xl: string;
  full: string;
}

export interface ThemeShadow {
  sm: string;
  md: string;
  lg: string;
  xl: string;
  xxl: string;
}

export interface ThemeTransition {
  duration: {
    shortest: number;
    shorter: number;
    short: number;
    standard: number;
    complex: number;
    enteringScreen: number;
    leavingScreen: number;
  };
  easing: {
    easeInOut: string;
    easeOut: string;
    easeIn: string;
    sharp: string;
  };
}

export interface ThemeZIndex {
  mobileStepper: number;
  speedDial: number;
  appBar: number;
  drawer: number;
  modal: number;
  snackbar: number;
  tooltip: number;
}

export interface Theme {
  name: string;
  type: 'light' | 'dark' | 'system';
  colors: ThemeColors;
  typography: ThemeTypography;
  spacing: ThemeSpacing;
  borderRadius: ThemeBorderRadius;
  shadow: ThemeShadow;
  transition: ThemeTransition;
  zIndex: ThemeZIndex;
}

export class ThemeManager extends EventEmitter {
  private static instance: ThemeManager;
  private currentTheme: Theme;
  private themes: Map<string, Theme>;
  private customThemes: Map<string, Theme>;

  private constructor() {
    super();
    this.themes = new Map();
    this.customThemes = new Map();
    this.currentTheme = this.getDefaultTheme();
    this.initializeThemes();
  }

  public static getInstance(): ThemeManager {
    if (!ThemeManager.instance) {
      ThemeManager.instance = new ThemeManager();
    }
    return ThemeManager.instance;
  }

  private getDefaultTheme(): Theme {
    return {
      name: 'default',
      type: 'light',
      colors: {
        primary: '#1976d2',
        secondary: '#dc004e',
        accent: '#ff4081',
        background: '#ffffff',
        surface: '#f5f5f5',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196f3',
        success: '#4caf50',
        text: {
          primary: 'rgba(0, 0, 0, 0.87)',
          secondary: 'rgba(0, 0, 0, 0.6)',
          disabled: 'rgba(0, 0, 0, 0.38)',
        },
        divider: 'rgba(0, 0, 0, 0.12)',
        border: 'rgba(0, 0, 0, 0.12)',
        shadow: 'rgba(0, 0, 0, 0.2)',
      },
      typography: {
        fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        fontSize: {
          xs: '0.75rem',
          sm: '0.875rem',
          md: '1rem',
          lg: '1.25rem',
          xl: '1.5rem',
        },
        fontWeight: {
          light: 300,
          regular: 400,
          medium: 500,
          bold: 700,
        },
        lineHeight: {
          tight: 1.25,
          normal: 1.5,
          relaxed: 1.75,
        },
        letterSpacing: {
          tighter: '-0.05em',
          tight: '-0.025em',
          normal: '0',
          wide: '0.025em',
          wider: '0.05em',
        },
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
        xxl: '3rem',
      },
      borderRadius: {
        none: '0',
        sm: '0.125rem',
        md: '0.25rem',
        lg: '0.5rem',
        xl: '1rem',
        full: '9999px',
      },
      shadow: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
        xxl: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
      },
      transition: {
        duration: {
          shortest: 150,
          shorter: 200,
          short: 250,
          standard: 300,
          complex: 375,
          enteringScreen: 225,
          leavingScreen: 195,
        },
        easing: {
          easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
          easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
          easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
          sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
        },
      },
      zIndex: {
        mobileStepper: 1000,
        speedDial: 1050,
        appBar: 1100,
        drawer: 1200,
        modal: 1300,
        snackbar: 1400,
        tooltip: 1500,
      },
    };
  }

  private async initializeThemes(): Promise<void> {
    try {
      await this.loadBuiltInThemes();
      await this.loadCustomThemes();
      await this.loadUserPreferences();
      this.setupThemeEvents();
    } catch (error) {
      console.error('Theme initialization failed:', error);
      this.emit('theme-error', error);
    }
  }

  private async loadBuiltInThemes(): Promise<void> {
    const themesPath = path.join(__dirname, 'themes');
    try {
      const files = await fs.readdir(themesPath);
      for (const file of files) {
        if (file.endsWith('.json')) {
          const themeData = await fs.readFile(path.join(themesPath, file), 'utf-8');
          const theme = JSON.parse(themeData);
          this.themes.set(theme.name, theme);
        }
      }
    } catch (error) {
      console.error('Failed to load built-in themes:', error);
    }
  }

  private async loadCustomThemes(): Promise<void> {
    const customThemesPath = path.join(app.getPath('userData'), 'themes');
    try {
      await fs.mkdir(customThemesPath, { recursive: true });
      const files = await fs.readdir(customThemesPath);
      for (const file of files) {
        if (file.endsWith('.json')) {
          const themeData = await fs.readFile(path.join(customThemesPath, file), 'utf-8');
          const theme = JSON.parse(themeData);
          this.customThemes.set(theme.name, theme);
        }
      }
    } catch (error) {
      console.error('Failed to load custom themes:', error);
    }
  }

  private async loadUserPreferences(): Promise<void> {
    try {
      const prefsPath = path.join(app.getPath('userData'), 'theme-preferences.json');
      const data = await fs.readFile(prefsPath, 'utf-8');
      const prefs = JSON.parse(data);
      if (prefs.currentTheme) {
        await this.setTheme(prefs.currentTheme);
      }
    } catch (error) {
      console.error('Failed to load theme preferences:', error);
    }
  }

  private setupThemeEvents(): void {
    app.on('browser-window-created', (event, window) => {
      this.applyThemeToWindow(window);
    });
  }

  private async applyThemeToWindow(window: BrowserWindow): Promise<void> {
    const css = this.generateThemeCSS();
    await window.webContents.insertCSS(css);
  }

  private generateThemeCSS(): string {
    const { colors, typography, spacing, borderRadius, shadow } = this.currentTheme;

    return `
      :root {
        /* Colors */
        --color-primary: ${colors.primary};
        --color-secondary: ${colors.secondary};
        --color-accent: ${colors.accent};
        --color-background: ${colors.background};
        --color-surface: ${colors.surface};
        --color-error: ${colors.error};
        --color-warning: ${colors.warning};
        --color-info: ${colors.info};
        --color-success: ${colors.success};
        --color-text-primary: ${colors.text.primary};
        --color-text-secondary: ${colors.text.secondary};
        --color-text-disabled: ${colors.text.disabled};
        --color-divider: ${colors.divider};
        --color-border: ${colors.border};
        --color-shadow: ${colors.shadow};

        /* Typography */
        --font-family: ${typography.fontFamily};
        --font-size-xs: ${typography.fontSize.xs};
        --font-size-sm: ${typography.fontSize.sm};
        --font-size-md: ${typography.fontSize.md};
        --font-size-lg: ${typography.fontSize.lg};
        --font-size-xl: ${typography.fontSize.xl};
        --font-weight-light: ${typography.fontWeight.light};
        --font-weight-regular: ${typography.fontWeight.regular};
        --font-weight-medium: ${typography.fontWeight.medium};
        --font-weight-bold: ${typography.fontWeight.bold};
        --line-height-tight: ${typography.lineHeight.tight};
        --line-height-normal: ${typography.lineHeight.normal};
        --line-height-relaxed: ${typography.lineHeight.relaxed};
        --letter-spacing-tighter: ${typography.letterSpacing.tighter};
        --letter-spacing-tight: ${typography.letterSpacing.tight};
        --letter-spacing-normal: ${typography.letterSpacing.normal};
        --letter-spacing-wide: ${typography.letterSpacing.wide};
        --letter-spacing-wider: ${typography.letterSpacing.wider};

        /* Spacing */
        --spacing-xs: ${spacing.xs};
        --spacing-sm: ${spacing.sm};
        --spacing-md: ${spacing.md};
        --spacing-lg: ${spacing.lg};
        --spacing-xl: ${spacing.xl};
        --spacing-xxl: ${spacing.xxl};

        /* Border Radius */
        --border-radius-none: ${borderRadius.none};
        --border-radius-sm: ${borderRadius.sm};
        --border-radius-md: ${borderRadius.md};
        --border-radius-lg: ${borderRadius.lg};
        --border-radius-xl: ${borderRadius.xl};
        --border-radius-full: ${borderRadius.full};

        /* Shadow */
        --shadow-sm: ${shadow.sm};
        --shadow-md: ${shadow.md};
        --shadow-lg: ${shadow.lg};
        --shadow-xl: ${shadow.xl};
        --shadow-xxl: ${shadow.xxl};
      }
    `;
  }

  public async setTheme(themeName: string): Promise<void> {
    const theme = this.themes.get(themeName) || this.customThemes.get(themeName);
    if (theme) {
      this.currentTheme = theme;
      this.emit('theme-changed', theme);

      // Apply theme to all windows
      BrowserWindow.getAllWindows().forEach(window => {
        this.applyThemeToWindow(window);
      });

      // Save preferences
      await this.saveUserPreferences();
    }
  }

  public async createCustomTheme(theme: Theme): Promise<void> {
    this.customThemes.set(theme.name, theme);
    const customThemesPath = path.join(app.getPath('userData'), 'themes');
    await fs.mkdir(customThemesPath, { recursive: true });
    await fs.writeFile(
      path.join(customThemesPath, `${theme.name}.json`),
      JSON.stringify(theme, null, 2)
    );
    this.emit('custom-theme-created', theme);
  }

  public async deleteCustomTheme(themeName: string): Promise<void> {
    if (this.customThemes.has(themeName)) {
      this.customThemes.delete(themeName);
      const themePath = path.join(app.getPath('userData'), 'themes', `${themeName}.json`);
      await fs.unlink(themePath);
      this.emit('custom-theme-deleted', themeName);
    }
  }

  public getCurrentTheme(): Theme {
    return { ...this.currentTheme };
  }

  public getAvailableThemes(): string[] {
    return [...this.themes.keys(), ...this.customThemes.keys()];
  }

  private async saveUserPreferences(): Promise<void> {
    const prefs = {
      currentTheme: this.currentTheme.name,
    };
    await fs.writeFile(
      path.join(app.getPath('userData'), 'theme-preferences.json'),
      JSON.stringify(prefs, null, 2)
    );
  }

  public async exportTheme(themeName: string): Promise<string> {
    const theme = this.themes.get(themeName) || this.customThemes.get(themeName);
    if (theme) {
      return JSON.stringify(theme, null, 2);
    }
    throw new Error(`Theme ${themeName} not found`);
  }

  public async importTheme(themeData: string): Promise<void> {
    const theme = JSON.parse(themeData);
    await this.createCustomTheme(theme);
    logger.info('Theme imported successfully', { themeName: theme.name });
  }

  public enableAutoThemeSwitch(): void {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

    const handleThemeChange = (e: MediaQueryListEvent) => {
      const newTheme = e.matches ? 'dark' : 'light';
      this.setTheme(newTheme);
      logger.info('Auto theme switch triggered', { newTheme });
    };

    mediaQuery.addEventListener('change', handleThemeChange);

    // Apply initial theme based on system preference
    const initialTheme = mediaQuery.matches ? 'dark' : 'light';
    this.setTheme(initialTheme);
  }

  public generateThemeFromImage(imageUrl: string): Promise<Theme> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';

      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');

          if (!ctx) {
            reject(new Error('Could not get canvas context'));
            return;
          }

          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);

          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          const colors = this.extractColorsFromImageData(imageData);

          const theme: Theme = {
            name: `Generated Theme ${Date.now()}`,
            colors: {
              primary: colors.dominant,
              secondary: colors.secondary,
              accent: colors.accent,
              background: colors.light,
              surface: colors.lighter,
              error: '#ef4444',
              warning: '#f59e0b',
              info: '#3b82f6',
              success: '#10b981',
              text: {
                primary: colors.dark,
                secondary: colors.medium,
                disabled: colors.light,
              },
              divider: colors.light,
              border: colors.medium,
              shadow: colors.dark,
            },
            typography: this.getDefaultTypography(),
            spacing: this.getDefaultSpacing(),
            borderRadius: this.getDefaultBorderRadius(),
            shadow: this.getDefaultShadow(),
            transition: this.getDefaultTransition(),
            zIndex: this.getDefaultZIndex(),
          };

          resolve(theme);
          logger.info('Theme generated from image', { imageUrl, colors });
        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      img.src = imageUrl;
    });
  }

  private extractColorsFromImageData(imageData: ImageData): {
    dominant: string;
    secondary: string;
    accent: string;
    light: string;
    lighter: string;
    medium: string;
    dark: string;
  } {
    const data = imageData.data;
    const colorCounts: { [key: string]: number } = {};

    // Sample every 10th pixel for performance
    for (let i = 0; i < data.length; i += 40) {
      const r = data[i];
      const g = data[i + 1];
      const b = data[i + 2];
      const alpha = data[i + 3];

      if (alpha > 128) {
        // Only consider non-transparent pixels
        const color = `rgb(${r},${g},${b})`;
        colorCounts[color] = (colorCounts[color] || 0) + 1;
      }
    }

    // Sort colors by frequency
    const sortedColors = Object.entries(colorCounts)
      .sort(([, a], [, b]) => b - a)
      .map(([color]) => color);

    return {
      dominant: sortedColors[0] || '#3b82f6',
      secondary: sortedColors[1] || '#6366f1',
      accent: sortedColors[2] || '#8b5cf6',
      light: '#f8fafc',
      lighter: '#ffffff',
      medium: '#64748b',
      dark: '#1e293b',
    };
  }

  public validateTheme(theme: Theme): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required properties
    if (!theme.name) errors.push('Theme name is required');
    if (!theme.colors) errors.push('Theme colors are required');
    if (!theme.typography) errors.push('Theme typography is required');

    // Check color contrast
    if (theme.colors) {
      const bgColor = theme.colors.background;
      const textColor = theme.colors.text.primary;

      if (bgColor && textColor) {
        const contrast = this.calculateColorContrast(bgColor, textColor);
        if (contrast < 4.5) {
          warnings.push('Low contrast between background and text colors');
        }
      }
    }

    // Check for accessibility issues
    if (theme.colors?.error && theme.colors?.success) {
      if (this.colorsAreSimilar(theme.colors.error, theme.colors.success)) {
        warnings.push('Error and success colors are too similar');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
    };
  }

  private calculateColorContrast(color1: string, color2: string): number {
    // Simplified contrast calculation
    // In a real implementation, use WCAG contrast algorithm
    return Math.random() * 10; // Placeholder
  }

  private colorsAreSimilar(color1: string, color2: string): boolean {
    // Simplified color similarity check
    return color1 === color2;
  }

  public getThemeAnalytics(): {
    totalThemes: number;
    customThemes: number;
    mostUsedTheme: string;
    themeUsageStats: { [themeName: string]: number };
  } {
    const themeUsage = configManager.get('theme.usage', {});
    const mostUsedTheme =
      Object.entries(themeUsage).sort(([, a], [, b]) => (b as number) - (a as number))[0]?.[0] ||
      'light';

    return {
      totalThemes: this.themes.size,
      customThemes: Array.from(this.themes.values()).filter(t => t.name.startsWith('Custom'))
        .length,
      mostUsedTheme,
      themeUsageStats: themeUsage,
    };
  }

  public scheduleThemeSwitch(themeName: string, time: Date): void {
    const now = new Date();
    const delay = time.getTime() - now.getTime();

    if (delay > 0) {
      setTimeout(() => {
        this.setTheme(themeName);
        logger.info('Scheduled theme switch executed', { themeName, scheduledTime: time });
      }, delay);

      logger.info('Theme switch scheduled', { themeName, scheduledTime: time, delay });
    }
  }

  private getDefaultTypography(): ThemeTypography {
    return {
      fontFamily: 'Inter, system-ui, sans-serif',
      fontSize: {
        xs: '0.75rem',
        sm: '0.875rem',
        md: '1rem',
        lg: '1.125rem',
        xl: '1.25rem',
      },
      fontWeight: {
        light: 300,
        regular: 400,
        medium: 500,
        bold: 700,
      },
      lineHeight: {
        tight: 1.25,
        normal: 1.5,
        relaxed: 1.75,
      },
      letterSpacing: {
        tighter: '-0.05em',
        tight: '-0.025em',
        normal: '0',
        wide: '0.025em',
        wider: '0.05em',
      },
    };
  }

  private getDefaultSpacing(): ThemeSpacing {
    return {
      xs: '0.25rem',
      sm: '0.5rem',
      md: '1rem',
      lg: '1.5rem',
      xl: '2rem',
      xxl: '3rem',
    };
  }

  private getDefaultBorderRadius(): ThemeBorderRadius {
    return {
      none: '0',
      sm: '0.125rem',
      md: '0.375rem',
      lg: '0.5rem',
      xl: '0.75rem',
      full: '9999px',
    };
  }

  private getDefaultShadow(): ThemeShadow {
    return {
      sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
      md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
      lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
      xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
      xxl: '0 25px 50px -12px rgb(0 0 0 / 0.25)',
    };
  }

  private getDefaultTransition(): ThemeTransition {
    return {
      duration: {
        shortest: 150,
        shorter: 200,
        short: 250,
        standard: 300,
        complex: 375,
        enteringScreen: 225,
        leavingScreen: 195,
      },
      easing: {
        easeInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
        easeOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
        easeIn: 'cubic-bezier(0.4, 0, 1, 1)',
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',
      },
    };
  }

  private getDefaultZIndex(): ThemeZIndex {
    return {
      mobileStepper: 1000,
      fab: 1050,
      speedDial: 1050,
      appBar: 1100,
      drawer: 1200,
      modal: 1300,
      snackbar: 1400,
      tooltip: 1500,
    };
  }
}
