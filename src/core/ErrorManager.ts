import { createHash } from 'crypto';
import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';

import * as Sentry from '@sentry/electron';
import { BrowserWindow, app, dialog } from 'electron';
import { AbortController } from 'node-abort-controller';

import { APP_CONFIG } from '../config/app.config';
import { logger } from '../logging/Logger';

type ErrorLevel = 'info' | 'warning' | 'error' | 'critical';
type ErrorCategory = 'network' | 'security' | 'performance' | 'ui' | 'system' | 'unknown';

// Legacy error types for backward compatibility
export const errorTypes = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
  NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
  CONFLICT_ERROR: 'CONFLICT_ERROR',
  RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  FILE_SYSTEM_ERROR: 'FILE_SYSTEM_ERROR',
  UNKNOWN_ERROR: 'UNKNOWN_ERROR',
} as const;

export type ErrorType = typeof errorTypes[keyof typeof errorTypes];

// Custom error class for backward compatibility
export class AppError extends Error {
  constructor(
    message: string,
    public code: ErrorType,
    public status: number = 500,
    public isOperational: boolean = true
  ) {
    super(message);
    this.name = 'AppError';
    Error.captureStackTrace(this, this.constructor);
  }
}

// Error context interface
export interface ErrorContext {
  component?: string;
  method?: string;
  user?: {
    id?: string;
    email?: string;
    username?: string;
  };
  tags?: Record<string, string>;
  level?: 'fatal' | 'error' | 'warning' | 'info' | 'debug';
  breadcrumbs?: Array<{
    category: string;
    message: string;
    level: string;
    timestamp: number;
  }>;
  [key: string]: any;
}

interface ErrorLog {
  timestamp: Date;
  level: ErrorLevel;
  category: ErrorCategory;
  message: string;
  stack?: string;
  metadata?: Record<string, any>;
}

interface SentryEvent {
  request?: {
    cookies?: any[];
    headers?: Record<string, string>;
  };
}

export interface ErrorReport {
  id: string;
  timestamp: number;
  type: string;
  message: string;
  stack?: string;
  source: string;
  url?: string;
  userAgent?: string;
  systemInfo: {
    platform: string;
    arch: string;
    version: string;
    memory: {
      total: number;
      free: number;
    };
    cpu: {
      model: string;
      cores: number;
      speed: number;
    };
  };
  browserInfo: {
    version: string;
    buildNumber: string;
    chromeVersion: string;
    electronVersion: string;
  };
  additionalData?: Record<string, any>;
}

export class ErrorManager extends EventEmitter {
  private static instance: ErrorManager;
  private errorLogs: ErrorLog[] = [];
  private maxLogSize: number = 1000;
  private isInitialized: boolean = false;
  private errorReports: Map<string, ErrorReport>;
  private crashReports: Map<string, ErrorReport>;
  private isReportingEnabled: boolean;
  private maxReports: number;
  private reportPath: string;

  private constructor() {
    super();
    this.errorReports = new Map();
    this.crashReports = new Map();
    this.isReportingEnabled = true;
    this.maxReports = 1000;
    this.reportPath = path.join(app.getPath('userData'), 'error-reports');
    this.initializeErrorHandling();
  }

  public static getInstance(): ErrorManager {
    if (!ErrorManager.instance) {
      ErrorManager.instance = new ErrorManager();
    }
    return ErrorManager.instance;
  }

  private async initializeErrorHandling(): Promise<void> {
    if (this.isInitialized) return;

    const { errorReporting } = APP_CONFIG;

    if (errorReporting.enabled) {
      // Initialize Sentry
      Sentry.init({
        dsn: process.env.SENTRY_DSN,
        environment: process.env.NODE_ENV,
        release: app.getVersion(),
        tracesSampleRate: 1.0,
        beforeSend: (event: SentryEvent) => {
          // Filter out sensitive information
          if (event.request?.cookies) {
            event.request.cookies = [];
          }
          if (event.request?.headers) {
            event.request.headers = {};
          }
          return event;
        },
      });

      // Set up global error handlers
      process.on('uncaughtException', error => {
        this.handleError(error, 'critical', 'system');
      });

      process.on('unhandledRejection', reason => {
        this.handleError(
          reason instanceof Error ? reason : new Error(String(reason)),
          'error',
          'system'
        );
      });

      // Set up window error handlers
      BrowserWindow.getAllWindows().forEach(window => {
        window.webContents.on('crashed', () => {
          this.handleError(new Error('Window crashed'), 'critical', 'system');
        });

        window.webContents.on('render-process-gone', (event, details) => {
          this.handleError(
            new Error(`Render process gone: ${details.reason}`),
            'critical',
            'system'
          );
        });
      });

      this.isInitialized = true;
    }

    try {
      await this.setupErrorHandling();
      await this.setupCrashHandling();
      await this.loadReports();
      this.setupErrorEvents();
    } catch (error) {
      console.error('Error handling initialization failed:', error);
      this.emit('initialization-error', error);
    }
  }

  private async setupErrorHandling(): Promise<void> {
    process.on('uncaughtException', error => {
      this.handleUncaughtException(error);
    });

    process.on('unhandledRejection', reason => {
      this.handleUnhandledRejection(reason);
    });

    app.on('render-process-gone', (event, webContents, details) => {
      this.handleRenderProcessGone(webContents, details);
    });

    app.on('child-process-gone', (event, details) => {
      this.handleChildProcessGone(details);
    });
  }

  private async setupCrashHandling(): Promise<void> {
    app.on('render-process-gone', (event, webContents, details) => {
      this.handleCrash(webContents, details);
    });

    app.on('child-process-gone', (event, details) => {
      this.handleCrash(null, details);
    });
  }

  private setupErrorEvents(): void {
    app.on('browser-window-created', (event, window) => {
      this.monitorWindowErrors(window);
    });
  }

  private monitorWindowErrors(window: BrowserWindow): void {
    window.webContents.on('crashed', (event, killed) => {
      this.handleWindowCrash(window, killed);
    });

    window.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      this.handlePageLoadError(window, errorCode, errorDescription);
    });

    window.webContents.on('did-fail-provisional-load', (event, errorCode, errorDescription) => {
      this.handleProvisionalLoadError(window, errorCode, errorDescription);
    });
  }

  private async handleUncaughtException(error: Error): Promise<void> {
    const report = await this.createErrorReport(error, 'uncaughtException');
    await this.saveErrorReport(report);
    this.emit('uncaught-exception', report);

    if (this.shouldShowErrorDialog()) {
      this.showErrorDialog('Uncaught Exception', error.message);
    }
  }

  private async handleUnhandledRejection(reason: any): Promise<void> {
    const error = reason instanceof Error ? reason : new Error(String(reason));
    const report = await this.createErrorReport(error, 'unhandledRejection');
    await this.saveErrorReport(report);
    this.emit('unhandled-rejection', report);

    if (this.shouldShowErrorDialog()) {
      this.showErrorDialog('Unhandled Promise Rejection', error.message);
    }
  }

  private async handleRenderProcessGone(
    webContents: Electron.WebContents,
    details: Electron.RenderProcessGoneDetails
  ): Promise<void> {
    const error = new Error(`Render process gone: ${details.reason}`);
    const report = await this.createErrorReport(error, 'renderProcessGone', {
      webContents,
      details,
    });
    await this.saveErrorReport(report);
    this.emit('render-process-gone', report);

    if (this.shouldShowErrorDialog()) {
      this.showErrorDialog('Render Process Crashed', `Reason: ${details.reason}`);
    }
  }

  private async handleChildProcessGone(details: Electron.Details): Promise<void> {
    const error = new Error(`Child process gone: ${details.reason}`);
    const report = await this.createErrorReport(error, 'childProcessGone', {
      details,
    });
    await this.saveErrorReport(report);
    this.emit('child-process-gone', report);

    if (this.shouldShowErrorDialog()) {
      this.showErrorDialog('Child Process Crashed', `Reason: ${details.reason}`);
    }
  }

  private async handleWindowCrash(window: BrowserWindow, killed: boolean): Promise<void> {
    const error = new Error(`Window crashed${killed ? ' (killed)' : ''}`);
    const report = await this.createErrorReport(error, 'windowCrash', {
      windowId: window.id,
      killed,
    });
    await this.saveErrorReport(report);
    this.emit('window-crash', report);

    if (this.shouldShowErrorDialog()) {
      this.showErrorDialog(
        'Window Crashed',
        killed ? 'Window was killed' : 'Window crashed unexpectedly'
      );
    }
  }

  private async handlePageLoadError(
    window: BrowserWindow,
    errorCode: number,
    errorDescription: string
  ): Promise<void> {
    const error = new Error(`Page load failed: ${errorDescription} (${errorCode})`);
    const report = await this.createErrorReport(error, 'pageLoadError', {
      windowId: window.id,
      errorCode,
      errorDescription,
      url: window.webContents.getURL(),
    });
    await this.saveErrorReport(report);
    this.emit('page-load-error', report);
  }

  private async handleProvisionalLoadError(
    window: BrowserWindow,
    errorCode: number,
    errorDescription: string
  ): Promise<void> {
    const error = new Error(`Provisional load failed: ${errorDescription} (${errorCode})`);
    const report = await this.createErrorReport(error, 'provisionalLoadError', {
      windowId: window.id,
      errorCode,
      errorDescription,
      url: window.webContents.getURL(),
    });
    await this.saveErrorReport(report);
    this.emit('provisional-load-error', report);
  }

  private async handleCrash(
    webContents: Electron.WebContents | null,
    details: Electron.Details
  ): Promise<void> {
    const error = new Error(`Crash occurred: ${details.reason}`);
    const report = await this.createErrorReport(error, 'crash', {
      webContents: webContents
        ? {
            id: webContents.id,
            url: webContents.getURL(),
          }
        : null,
      details,
    });
    await this.saveCrashReport(report);
    this.emit('crash', report);

    if (this.shouldShowErrorDialog()) {
      this.showErrorDialog('Application Crashed', `Reason: ${details.reason}`);
    }
  }

  private async createErrorReport(
    error: Error,
    type: string,
    additionalData?: Record<string, any>
  ): Promise<ErrorReport> {
    const report: ErrorReport = {
      id: createHash('sha256').update(`${Date.now()}-${Math.random()}`).digest('hex'),
      timestamp: Date.now(),
      type,
      message: error.message,
      stack: error.stack,
      source: 'main',
      systemInfo: await this.getSystemInfo(),
      browserInfo: this.getBrowserInfo(),
      additionalData,
    };

    return report;
  }

  private async getSystemInfo(): Promise<ErrorReport['systemInfo']> {
    const os = require('os');
    return {
      platform: process.platform,
      arch: process.arch,
      version: os.release(),
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
      },
      cpu: {
        model: os.cpus()[0].model,
        cores: os.cpus().length,
        speed: os.cpus()[0].speed,
      },
    };
  }

  private getBrowserInfo(): ErrorReport['browserInfo'] {
    return {
      version: app.getVersion(),
      buildNumber: process.getSystemVersion(),
      chromeVersion: process.versions.chrome,
      electronVersion: process.versions.electron,
    };
  }

  private async saveErrorReport(report: ErrorReport): Promise<void> {
    if (!this.isReportingEnabled) return;

    this.errorReports.set(report.id, report);
    await this.saveReports();

    if (this.errorReports.size > this.maxReports) {
      const oldestReport = Array.from(this.errorReports.entries())[0];
      this.errorReports.delete(oldestReport[0]);
    }
  }

  private async saveCrashReport(report: ErrorReport): Promise<void> {
    if (!this.isReportingEnabled) return;

    this.crashReports.set(report.id, report);
    await this.saveReports();

    if (this.crashReports.size > this.maxReports) {
      const oldestReport = Array.from(this.crashReports.entries())[0];
      this.crashReports.delete(oldestReport[0]);
    }
  }

  private async loadReports(): Promise<void> {
    try {
      await fs.mkdir(this.reportPath, { recursive: true });

      const errorReportsPath = path.join(this.reportPath, 'error-reports.json');
      const crashReportsPath = path.join(this.reportPath, 'crash-reports.json');

      try {
        const errorReportsData = await fs.readFile(errorReportsPath, 'utf-8');
        const errorReports = JSON.parse(errorReportsData);
        this.errorReports = new Map(Object.entries(errorReports));
      } catch (error) {
        this.errorReports = new Map();
      }

      try {
        const crashReportsData = await fs.readFile(crashReportsPath, 'utf-8');
        const crashReports = JSON.parse(crashReportsData);
        this.crashReports = new Map(Object.entries(crashReports));
      } catch (error) {
        this.crashReports = new Map();
      }
    } catch (error) {
      console.error('Failed to load error reports:', error);
    }
  }

  private async saveReports(): Promise<void> {
    try {
      await fs.mkdir(this.reportPath, { recursive: true });

      const errorReportsPath = path.join(this.reportPath, 'error-reports.json');
      const crashReportsPath = path.join(this.reportPath, 'crash-reports.json');

      await fs.writeFile(
        errorReportsPath,
        JSON.stringify(Object.fromEntries(this.errorReports), null, 2)
      );

      await fs.writeFile(
        crashReportsPath,
        JSON.stringify(Object.fromEntries(this.crashReports), null, 2)
      );
    } catch (error) {
      console.error('Failed to save error reports:', error);
    }
  }

  private shouldShowErrorDialog(): boolean {
    return process.type === 'browser' && !app.isPackaged;
  }

  private showErrorDialog(title: string, message: string): void {
    dialog.showErrorBox(title, message);
  }

  public enableErrorReporting(): void {
    this.isReportingEnabled = true;
  }

  public disableErrorReporting(): void {
    this.isReportingEnabled = false;
  }

  public isErrorReportingEnabled(): boolean {
    return this.isReportingEnabled;
  }

  public setMaxReports(max: number): void {
    this.maxReports = max;
  }

  public getMaxReports(): number {
    return this.maxReports;
  }

  public getErrorReports(): ErrorReport[] {
    return Array.from(this.errorReports.values());
  }

  public getCrashReports(): ErrorReport[] {
    return Array.from(this.crashReports.values());
  }

  public async clearErrorReports(): Promise<void> {
    this.errorReports.clear();
    await this.saveReports();
  }

  public async clearCrashReports(): Promise<void> {
    this.crashReports.clear();
    await this.saveReports();
  }

  public async exportReports(): Promise<string> {
    const reports = {
      errorReports: Object.fromEntries(this.errorReports),
      crashReports: Object.fromEntries(this.crashReports),
    };
    return JSON.stringify(reports, null, 2);
  }

  public async importReports(reportsData: string): Promise<void> {
    const reports = JSON.parse(reportsData);
    this.errorReports = new Map(Object.entries(reports.errorReports));
    this.crashReports = new Map(Object.entries(reports.crashReports));
    await this.saveReports();
  }

  public handleError(
    error: Error,
    level: ErrorLevel = 'error',
    category: ErrorCategory = 'unknown',
    metadata?: Record<string, any>
  ): void {
    const errorLog: ErrorLog = {
      timestamp: new Date(),
      level,
      category,
      message: error.message,
      stack: error.stack,
      metadata,
    };

    // Add to local logs
    this.errorLogs.push(errorLog);
    if (this.errorLogs.length > this.maxLogSize) {
      this.errorLogs.shift();
    }

    // Log to console
    console.error(`[${level.toUpperCase()}] ${error.message}`, {
      category,
      stack: error.stack,
      metadata,
    });

    // Send to Sentry if enabled
    if (APP_CONFIG.errorReporting.enabled) {
      Sentry.withScope((scope: any) => {
        scope.setLevel(level);
        scope.setTag('category', category);
        if (metadata) {
          Object.entries(metadata).forEach(([key, value]) => {
            scope.setExtra(key, value);
          });
        }
        Sentry.captureException(error);
      });
    }

    // Handle critical errors
    if (level === 'critical') {
      this.handleCriticalError(error);
    }
  }

  private handleCriticalError(error: Error): void {
    // Show error dialog to user
    const windows = BrowserWindow.getAllWindows();
    if (windows.length > 0) {
      windows[0].webContents.executeJavaScript(`
        window.dispatchEvent(new CustomEvent('critical-error', {
          detail: {
            message: ${JSON.stringify(error.message)},
            stack: ${JSON.stringify(error.stack)},
          }
        }));
      `);
    }

    // Attempt to recover
    this.attemptRecovery();
  }

  private async attemptRecovery(): Promise<void> {
    try {
      // Clear cache
      const defaultSession = BrowserWindow.getAllWindows()[0]?.webContents.session;
      if (defaultSession) {
        await defaultSession.clearCache();
      }

      // Reload windows
      BrowserWindow.getAllWindows().forEach(window => {
        if (!window.isDestroyed()) {
          window.reload();
        }
      });
    } catch (error) {
      console.error('Recovery attempt failed:', error);
    }
  }

  public getErrorLogs(): ErrorLog[] {
    return [...this.errorLogs];
  }

  public clearErrorLogs(): void {
    this.errorLogs = [];
  }

  public setMaxLogSize(size: number): void {
    this.maxLogSize = size;
    if (this.errorLogs.length > size) {
      this.errorLogs = this.errorLogs.slice(-size);
    }
  }

  public getErrorStats(): Record<ErrorLevel, number> {
    const stats: Record<ErrorLevel, number> = {
      info: 0,
      warning: 0,
      error: 0,
      critical: 0,
    };

    this.errorLogs.forEach(log => {
      stats[log.level]++;
    });

    return stats;
  }

  public getErrorsByCategory(): Record<ErrorCategory, number> {
    const stats: Record<ErrorCategory, number> = {
      network: 0,
      security: 0,
      performance: 0,
      ui: 0,
      system: 0,
      unknown: 0,
    };

    this.errorLogs.forEach(log => {
      stats[log.category]++;
    });

    return stats;
  }

  public getRecentErrors(count: number = 10): ErrorLog[] {
    return this.errorLogs.slice(-count);
  }

  public getErrorsByLevel(level: ErrorLevel): ErrorLog[] {
    return this.errorLogs.filter(log => log.level === level);
  }

  public getErrorsByCategoryType(category: ErrorCategory): ErrorLog[] {
    return this.errorLogs.filter(log => log.category === category);
  }
}

// Create and export singleton instance
export const errorManager = ErrorManager.getInstance();

// Export legacy interfaces for backward compatibility
export const trackError = (error: Error, context?: any) => {
  errorManager.handleError(error, 'error', 'unknown', context);
};

export const errorHandler = {
  handleError: (type: string, error: Error, metadata?: any) => {
    errorManager.handleError(error, 'error', 'unknown', { type, ...metadata });
  },
  wrapSync: (fn: Function, context: string) => {
    return (...args: any[]) => {
      try {
        return fn(...args);
      } catch (error) {
        errorManager.handleError(error as Error, 'error', 'unknown', { context });
        throw error;
      }
    };
  },
  wrapAsync: (fn: Function, context: string) => {
    return async (...args: any[]) => {
      try {
        return await fn(...args);
      } catch (error) {
        errorManager.handleError(error as Error, 'error', 'unknown', { context });
        throw error;
      }
    };
  },
};
