import { createHash } from 'crypto';
import { EventEmitter } from 'events';
import { createReadStream, createWriteStream, promises as fs } from 'fs';
import * as path from 'path';
import { pipeline } from 'stream/promises';
import { createGunzip, createGzip } from 'zlib';

import { app } from 'electron';

interface DownloadItem {
  id: string;
  url: string;
  filename: string;
  path: string;
  size: number;
  downloaded: number;
  status: 'pending' | 'downloading' | 'paused' | 'completed' | 'error';
  speed: number;
  progress: number;
  startTime: number;
  endTime?: number;
  error?: string;
  metadata: {
    contentType: string;
    lastModified?: string;
    etag?: string;
    category: string;
    tags: string[];
    priority: number;
  };
}

interface DownloadSettings {
  maxConcurrent: number;
  defaultPath: string;
  autoOpen: boolean;
  autoDelete: boolean;
  retryCount: number;
  retryDelay: number;
  speedLimit: number;
  categories: {
    documents: string;
    images: string;
    videos: string;
    audio: string;
    archives: string;
    other: string;
  };
  compression: {
    enabled: boolean;
    level: number;
    threshold: number;
  };
  integrity: {
    check: boolean;
    algorithm: string;
  };
}

export class DownloadManager extends EventEmitter {
  private static instance: DownloadManager;
  private downloads: Map<string, DownloadItem>;
  private settings: DownloadSettings;
  private isInitialized: boolean = false;
  private activeDownloads: Set<string> = new Set();
  private downloadQueue: string[] = [];

  private constructor() {
    super();
    this.downloads = new Map();
    this.settings = {
      maxConcurrent: 3,
      defaultPath: path.join(app.getPath('downloads')),
      autoOpen: false,
      autoDelete: false,
      retryCount: 3,
      retryDelay: 5000,
      speedLimit: 0,
      categories: {
        documents: path.join(app.getPath('downloads'), 'documents'),
        images: path.join(app.getPath('downloads'), 'images'),
        videos: path.join(app.getPath('downloads'), 'videos'),
        audio: path.join(app.getPath('downloads'), 'audio'),
        archives: path.join(app.getPath('downloads'), 'archives'),
        other: path.join(app.getPath('downloads'), 'other'),
      },
      compression: {
        enabled: true,
        level: 6,
        threshold: 1024 * 1024, // 1MB
      },
      integrity: {
        check: true,
        algorithm: 'sha256',
      },
    };
  }

  public static getInstance(): DownloadManager {
    if (!DownloadManager.instance) {
      DownloadManager.instance = new DownloadManager();
    }
    return DownloadManager.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      await this.loadSettings();
      await this.loadDownloads();
      await this.setupDirectories();
      this.isInitialized = true;
      this.emit('initialized');
    } catch (error) {
      console.error('Failed to initialize DownloadManager:', error);
      throw error;
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'download-settings.json');
      const data = await fs.readFile(settingsPath, 'utf-8');
      this.settings = { ...this.settings, ...JSON.parse(data) };
    } catch (error) {
      await this.saveSettings();
    }
  }

  private async saveSettings(): Promise<void> {
    const settingsPath = path.join(app.getPath('userData'), 'download-settings.json');
    await fs.writeFile(settingsPath, JSON.stringify(this.settings, null, 2));
  }

  private async loadDownloads(): Promise<void> {
    try {
      const downloadsPath = path.join(app.getPath('userData'), 'downloads.json');
      const data = await fs.readFile(downloadsPath, 'utf-8');
      const downloads = JSON.parse(data);

      for (const download of downloads) {
        this.downloads.set(download.id, download);
      }
    } catch (error) {
      await this.saveDownloads();
    }
  }

  private async saveDownloads(): Promise<void> {
    const downloadsPath = path.join(app.getPath('userData'), 'downloads.json');
    await fs.writeFile(downloadsPath, JSON.stringify(Array.from(this.downloads.values()), null, 2));
  }

  private async setupDirectories(): Promise<void> {
    for (const category of Object.values(this.settings.categories)) {
      await fs.mkdir(category, { recursive: true });
    }
  }

  public async startDownload(
    url: string,
    options: Partial<DownloadItem> = {}
  ): Promise<DownloadItem> {
    const download: DownloadItem = {
      id: Math.random().toString(36).substr(2, 9),
      url,
      filename: path.basename(url),
      path: this.settings.defaultPath,
      size: 0,
      downloaded: 0,
      status: 'pending',
      speed: 0,
      progress: 0,
      startTime: Date.now(),
      metadata: {
        contentType: '',
        category: 'other',
        tags: [],
        priority: 0,
      },
      ...options,
    };

    this.downloads.set(download.id, download);
    this.downloadQueue.push(download.id);
    await this.saveDownloads();
    this.emit('download-added', download);

    if (this.activeDownloads.size < this.settings.maxConcurrent) {
      await this.processNextDownload();
    }

    return download;
  }

  private async processNextDownload(): Promise<void> {
    if (this.downloadQueue.length === 0) return;

    const downloadId = this.downloadQueue.shift()!;
    const download = this.downloads.get(downloadId)!;

    try {
      download.status = 'downloading';
      this.activeDownloads.add(downloadId);
      this.emit('download-started', download);

      const response = await fetch(download.url);
      download.size = parseInt(response.headers.get('content-length') || '0');
      download.metadata.contentType = response.headers.get('content-type') || '';
      download.metadata.lastModified = response.headers.get('last-modified') || undefined;
      download.metadata.etag = response.headers.get('etag') || undefined;

      const category = this.getCategoryFromContentType(download.metadata.contentType);
      download.metadata.category = category;
      download.path = path.join(this.settings.categories[category], download.filename);

      const fileStream = createWriteStream(download.path);
      const downloadStream = response.body!;

      let lastUpdate = Date.now();
      let downloadedSinceLastUpdate = 0;

      for await (const chunk of downloadStream) {
        if (download.status === 'paused') {
          await new Promise(resolve => setTimeout(resolve, 1000));
          continue;
        }

        if (download.status === 'error') {
          break;
        }

        await fileStream.write(chunk);
        download.downloaded += chunk.length;
        downloadedSinceLastUpdate += chunk.length;

        const now = Date.now();
        if (now - lastUpdate >= 1000) {
          download.speed = downloadedSinceLastUpdate;
          download.progress = (download.downloaded / download.size) * 100;
          downloadedSinceLastUpdate = 0;
          lastUpdate = now;
          this.emit('download-progress', download);
        }
      }

      await fileStream.end();

      if (
        this.settings.compression.enabled &&
        download.size > this.settings.compression.threshold
      ) {
        await this.compressFile(download);
      }

      if (this.settings.integrity.check) {
        await this.verifyIntegrity(download);
      }

      download.status = 'completed';
      download.endTime = Date.now();
      this.emit('download-completed', download);
    } catch (error) {
      download.status = 'error';
      download.error = error.message;
      this.emit('download-error', download, error);

      if (this.settings.retryCount > 0) {
        setTimeout(() => {
          this.retryDownload(download);
        }, this.settings.retryDelay);
      }
    } finally {
      this.activeDownloads.delete(downloadId);
      await this.saveDownloads();
      await this.processNextDownload();
    }
  }

  private getCategoryFromContentType(contentType: string): keyof DownloadSettings['categories'] {
    if (contentType.startsWith('image/')) return 'images';
    if (contentType.startsWith('video/')) return 'videos';
    if (contentType.startsWith('audio/')) return 'audio';
    if (contentType.includes('pdf') || contentType.includes('document')) return 'documents';
    if (contentType.includes('zip') || contentType.includes('archive')) return 'archives';
    return 'other';
  }

  private async compressFile(download: DownloadItem): Promise<void> {
    const compressedPath = `${download.path}.gz`;
    await pipeline(
      createReadStream(download.path),
      createGzip({ level: this.settings.compression.level }),
      createWriteStream(compressedPath)
    );
    await fs.unlink(download.path);
    download.path = compressedPath;
  }

  private async verifyIntegrity(download: DownloadItem): Promise<void> {
    const hash = createHash(this.settings.integrity.algorithm);
    const fileStream = createReadStream(download.path);

    for await (const chunk of fileStream) {
      hash.update(chunk);
    }

    const fileHash = hash.digest('hex');
    // TODO: Compare with expected hash if available
  }

  private async retryDownload(download: DownloadItem): Promise<void> {
    download.status = 'pending';
    download.error = undefined;
    this.downloadQueue.unshift(download.id);
    await this.saveDownloads();
    this.emit('download-retrying', download);
    await this.processNextDownload();
  }

  public async pauseDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download not found: ${downloadId}`);
    }

    if (download.status === 'downloading') {
      download.status = 'paused';
      await this.saveDownloads();
      this.emit('download-paused', download);
    }
  }

  public async resumeDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download not found: ${downloadId}`);
    }

    if (download.status === 'paused') {
      download.status = 'downloading';
      await this.saveDownloads();
      this.emit('download-resumed', download);
    }
  }

  public async cancelDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download not found: ${downloadId}`);
    }

    download.status = 'error';
    download.error = 'Download cancelled';
    await this.saveDownloads();
    this.emit('download-cancelled', download);

    try {
      await fs.unlink(download.path);
    } catch (error) {
      console.error(`Failed to delete file for download ${downloadId}:`, error);
    }
  }

  public async deleteDownload(downloadId: string): Promise<void> {
    const download = this.downloads.get(downloadId);
    if (!download) {
      throw new Error(`Download not found: ${downloadId}`);
    }

    try {
      await fs.unlink(download.path);
    } catch (error) {
      console.error(`Failed to delete file for download ${downloadId}:`, error);
    }

    this.downloads.delete(downloadId);
    await this.saveDownloads();
    this.emit('download-deleted', download);
  }

  public getDownload(downloadId: string): DownloadItem | undefined {
    return this.downloads.get(downloadId);
  }

  public getAllDownloads(): DownloadItem[] {
    return Array.from(this.downloads.values());
  }

  public getActiveDownloads(): DownloadItem[] {
    return Array.from(this.activeDownloads).map(id => this.downloads.get(id)!);
  }

  public getSettings(): DownloadSettings {
    return { ...this.settings };
  }

  public async updateSettings(settings: Partial<DownloadSettings>): Promise<void> {
    this.settings = { ...this.settings, ...settings };
    await this.saveSettings();
    await this.setupDirectories();
  }

  public cleanup(): void {
    // Cleanup any pending downloads
    for (const download of this.downloads.values()) {
      if (download.status === 'downloading' || download.status === 'paused') {
        this.cancelDownload(download.id);
      }
    }
  }
}
