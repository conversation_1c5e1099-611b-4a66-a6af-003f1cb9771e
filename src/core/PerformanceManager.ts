import { exec, spawn } from 'child_process';
import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import { createServer as createHttpServer } from 'http';
import { createServer as createHttpsServer } from 'https';
import { createServer } from 'net';
import * as path from 'path';
import { promisify } from 'util';
import { Worker } from 'worker_threads';

import { BrowserWindow, app, session } from 'electron';

import { logger } from '../logging/Logger';

export interface PerformanceConfig {
  enableHardwareAcceleration: boolean;
  enableGpuAcceleration: boolean;
  enableWebGL: boolean;
  enableWebGL2: boolean;
  enableWebGPU: boolean;
  enableWebAssembly: boolean;
  enableSharedArrayBuffer: boolean;
  enableWebWorkers: boolean;
  enableServiceWorkers: boolean;
  enableBackgroundThrottling: boolean;
  enableBackgroundHibernation: boolean;
  maxMemoryUsage: number;
  maxConcurrentDownloads: number;
  maxConcurrentUploads: number;
  cacheSize: number;
  diskCacheSize: number;
  memoryCacheSize: number;
}

export interface PerformanceSettings {
  enabled: boolean;
  profiling: {
    enabled: boolean;
    interval: number;
    retention: number;
  };
  optimization: {
    enabled: boolean;
    autoOptimize: boolean;
    optimizationLevel: 'low' | 'medium' | 'high';
  };
  monitoring: {
    enabled: boolean;
    metrics: boolean;
    alerts: boolean;
    thresholds: {
      cpu: number;
      memory: number;
      disk: number;
      network: number;
    };
  };
  storage: {
    enabled: boolean;
    path: string;
  };
}

export interface PerformanceMetrics {
  cpu: {
    usage: number;
    temperature: number;
    frequency: number;
    cores: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
    available: number;
    cached: number;
    buffers: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    readSpeed: number;
    writeSpeed: number;
  };
  network: {
    downloadSpeed: number;
    uploadSpeed: number;
    latency: number;
    packetsLost: number;
  };
  gpu: {
    usage: number;
    memory: number;
    temperature: number;
  };
  browser: {
    tabCount: number;
    extensionCount: number;
    processCount: number;
    memoryUsage: number;
  };
}

export class PerformanceManager extends EventEmitter {
  private static instance: PerformanceManager;
  private config: PerformanceConfig;
  private settings: PerformanceSettings;
  private performanceMetrics: Map<string, number>;
  private resourceUsage: Map<string, number>;
  private currentMetrics: PerformanceMetrics;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private workers: Map<string, Worker> = new Map();

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.settings = this.getDefaultSettings();
    this.performanceMetrics = new Map();
    this.resourceUsage = new Map();
    this.currentMetrics = this.getEmptyMetrics();
    this.initializePerformance();
  }

  public static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  private getDefaultConfig(): PerformanceConfig {
    return {
      enableHardwareAcceleration: true,
      enableGpuAcceleration: true,
      enableWebGL: true,
      enableWebGL2: true,
      enableWebGPU: true,
      enableWebAssembly: true,
      enableSharedArrayBuffer: true,
      enableWebWorkers: true,
      enableServiceWorkers: true,
      enableBackgroundThrottling: true,
      enableBackgroundHibernation: true,
      maxMemoryUsage: 2048, // MB
      maxConcurrentDownloads: 6,
      maxConcurrentUploads: 4,
      cacheSize: 1024, // MB
      diskCacheSize: 512, // MB
      memoryCacheSize: 256, // MB
    };
  }

  private async initializePerformance(): Promise<void> {
    try {
      await this.setupHardwareAcceleration();
      await this.setupCache();
      await this.setupResourceLimits();
      this.setupPerformanceMonitoring();
    } catch (error) {
      console.error('Performance initialization failed:', error);
      this.emit('performance-error', error);
    }
  }

  private async setupHardwareAcceleration(): Promise<void> {
    if (this.config.enableHardwareAcceleration) {
      app.commandLine.appendSwitch('enable-gpu-rasterization');
      app.commandLine.appendSwitch('enable-zero-copy');
      app.commandLine.appendSwitch('ignore-gpu-blocklist');
      app.commandLine.appendSwitch('enable-gpu-compositing');
    }

    if (this.config.enableGpuAcceleration) {
      app.commandLine.appendSwitch('enable-gpu');
      app.commandLine.appendSwitch('enable-gpu-sandbox');
    }
  }

  private async setupCache(): Promise<void> {
    const cachePath = path.join(app.getPath('userData'), 'Cache');
    await fs.mkdir(cachePath, { recursive: true });

    session.defaultSession.setCacheSize(this.config.cacheSize * 1024 * 1024);
    session.defaultSession.setDiskCacheSize(this.config.diskCacheSize * 1024 * 1024);
    session.defaultSession.setMemoryCacheSize(this.config.memoryCacheSize * 1024 * 1024);
  }

  private async setupResourceLimits(): Promise<void> {
    app.commandLine.appendSwitch('js-flags', `--max-old-space-size=${this.config.maxMemoryUsage}`);
    app.commandLine.appendSwitch(
      'max-concurrent-downloads',
      this.config.maxConcurrentDownloads.toString()
    );
    app.commandLine.appendSwitch(
      'max-concurrent-uploads',
      this.config.maxConcurrentUploads.toString()
    );
  }

  private setupPerformanceMonitoring(): void {
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 5000);

    app.on('browser-window-created', (event, window) => {
      this.monitorWindowPerformance(window);
    });
  }

  private async collectPerformanceMetrics(): Promise<void> {
    const metrics = await this.getSystemMetrics();
    this.performanceMetrics = new Map(Object.entries(metrics));
    this.emit('metrics-updated', this.performanceMetrics);
  }

  private async getSystemMetrics(): Promise<Record<string, number>> {
    const metrics: Record<string, number> = {
      cpuUsage: process.cpuUsage().user / 1000000,
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
      uptime: process.uptime(),
    };

    return metrics;
  }

  private monitorWindowPerformance(window: BrowserWindow): void {
    window.webContents.on('did-finish-load', () => {
      this.measurePageLoadPerformance(window);
    });

    window.webContents.on('did-start-loading', () => {
      this.emit('page-load-start', window.id);
    });

    window.webContents.on('did-stop-loading', () => {
      this.emit('page-load-end', window.id);
    });
  }

  private async measurePageLoadPerformance(window: BrowserWindow): Promise<void> {
    const metrics = await window.webContents.executeJavaScript(`
      {
        navigationStart: performance.timing.navigationStart,
        loadEventEnd: performance.timing.loadEventEnd,
        domComplete: performance.timing.domComplete,
        domInteractive: performance.timing.domInteractive,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime
      }
    `);

    this.emit('page-performance', {
      windowId: window.id,
      metrics,
    });
  }

  public getPerformanceConfig(): PerformanceConfig {
    return { ...this.config };
  }

  public updatePerformanceConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config-updated', this.config);
  }

  public getPerformanceMetrics(): Map<string, number> {
    return new Map(this.performanceMetrics);
  }

  public getResourceUsage(): Map<string, number> {
    return new Map(this.resourceUsage);
  }

  public async optimizePerformance(): Promise<void> {
    try {
      await this.clearCache();
      await this.defragmentCache();
      await this.optimizeMemory();
      this.emit('optimization-complete');
    } catch (error) {
      console.error('Performance optimization failed:', error);
      this.emit('optimization-error', error);
    }
  }

  private async clearCache(): Promise<void> {
    await session.defaultSession.clearCache();
  }

  private async defragmentCache(): Promise<void> {
    // Implement cache defragmentation logic
  }

  private async optimizeMemory(): Promise<void> {
    if (global.gc) {
      global.gc();
    }
  }

  public cleanup(): void {
    // Stop monitoring
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    // Cleanup workers
    this.workers.forEach(worker => {
      worker.terminate();
    });
    this.workers.clear();

    // Cleanup resources
    this.performanceMetrics.clear();
    this.resourceUsage.clear();
  }

  // Enhanced monitoring methods
  private getDefaultSettings(): PerformanceSettings {
    return {
      enabled: true,
      profiling: {
        enabled: false,
        interval: 1000,
        retention: 3600000, // 1 hour
      },
      optimization: {
        enabled: true,
        autoOptimize: true,
        optimizationLevel: 'medium',
      },
      monitoring: {
        enabled: true,
        metrics: true,
        alerts: true,
        thresholds: {
          cpu: 80,
          memory: 85,
          disk: 90,
          network: 100,
        },
      },
      storage: {
        enabled: true,
        path: path.join(app.getPath('userData'), 'performance'),
      },
    };
  }

  private getEmptyMetrics(): PerformanceMetrics {
    return {
      cpu: { usage: 0, temperature: 0, frequency: 0, cores: 0 },
      memory: { total: 0, used: 0, free: 0, available: 0, cached: 0, buffers: 0 },
      disk: { total: 0, used: 0, free: 0, readSpeed: 0, writeSpeed: 0 },
      network: { downloadSpeed: 0, uploadSpeed: 0, latency: 0, packetsLost: 0 },
      gpu: { usage: 0, memory: 0, temperature: 0 },
      browser: { tabCount: 0, extensionCount: 0, processCount: 0, memoryUsage: 0 },
    };
  }

  public async startMonitoring(): Promise<void> {
    if (!this.settings.monitoring.enabled) return;

    this.monitoringInterval = setInterval(async () => {
      try {
        await this.collectMetrics();
        this.checkThresholds();
        this.emit('metrics-updated', this.currentMetrics);
      } catch (error) {
        logger.error('Failed to collect performance metrics:', error);
      }
    }, this.settings.profiling.interval);

    logger.info('Performance monitoring started');
  }

  public stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
      logger.info('Performance monitoring stopped');
    }
  }

  private async collectMetrics(): Promise<void> {
    // Collect system metrics
    this.currentMetrics.cpu = await this.getCpuMetrics();
    this.currentMetrics.memory = await this.getMemoryMetrics();
    this.currentMetrics.disk = await this.getDiskMetrics();
    this.currentMetrics.network = await this.getNetworkMetrics();
    this.currentMetrics.gpu = await this.getGpuMetrics();
    this.currentMetrics.browser = await this.getBrowserMetrics();
  }

  private async getCpuMetrics(): Promise<PerformanceMetrics['cpu']> {
    try {
      const execAsync = promisify(exec);

      // Get CPU usage (cross-platform)
      let usage = 0;
      if (process.platform === 'win32') {
        const { stdout } = await execAsync('wmic cpu get loadpercentage /value');
        const match = stdout.match(/LoadPercentage=(\d+)/);
        usage = match ? parseInt(match[1]) : 0;
      } else {
        const { stdout } = await execAsync('top -bn1 | grep "Cpu(s)" | awk \'{print $2}\' | awk -F\'%\' \'{print $1}\'');
        usage = parseFloat(stdout.trim()) || 0;
      }

      return {
        usage,
        temperature: 0, // Would need hardware-specific implementation
        frequency: 0,   // Would need hardware-specific implementation
        cores: require('os').cpus().length,
      };
    } catch (error) {
      logger.error('Failed to get CPU metrics:', error);
      return { usage: 0, temperature: 0, frequency: 0, cores: 0 };
    }
  }

  private async getMemoryMetrics(): Promise<PerformanceMetrics['memory']> {
    try {
      const os = require('os');
      const total = os.totalmem();
      const free = os.freemem();
      const used = total - free;

      return {
        total,
        used,
        free,
        available: free,
        cached: 0,
        buffers: 0,
      };
    } catch (error) {
      logger.error('Failed to get memory metrics:', error);
      return { total: 0, used: 0, free: 0, available: 0, cached: 0, buffers: 0 };
    }
  }

  private async getDiskMetrics(): Promise<PerformanceMetrics['disk']> {
    try {
      const stats = await fs.statfs(app.getPath('userData'));
      return {
        total: stats.bavail * stats.bsize,
        used: 0,
        free: stats.bavail * stats.bsize,
        readSpeed: 0,
        writeSpeed: 0,
      };
    } catch (error) {
      logger.error('Failed to get disk metrics:', error);
      return { total: 0, used: 0, free: 0, readSpeed: 0, writeSpeed: 0 };
    }
  }

  private async getNetworkMetrics(): Promise<PerformanceMetrics['network']> {
    // Network metrics would require more complex implementation
    return {
      downloadSpeed: 0,
      uploadSpeed: 0,
      latency: 0,
      packetsLost: 0,
    };
  }

  private async getGpuMetrics(): Promise<PerformanceMetrics['gpu']> {
    // GPU metrics would require hardware-specific implementation
    return {
      usage: 0,
      memory: 0,
      temperature: 0,
    };
  }

  private async getBrowserMetrics(): Promise<PerformanceMetrics['browser']> {
    const windows = BrowserWindow.getAllWindows();
    const tabCount = windows.reduce((count, window) => {
      return count + (window.webContents ? 1 : 0);
    }, 0);

    return {
      tabCount,
      extensionCount: 0, // Would need extension manager integration
      processCount: process.pid ? 1 : 0,
      memoryUsage: process.memoryUsage().heapUsed,
    };
  }

  private checkThresholds(): void {
    const thresholds = this.settings.monitoring.thresholds;

    if (this.currentMetrics.cpu.usage > thresholds.cpu) {
      this.emit('threshold-exceeded', { type: 'cpu', value: this.currentMetrics.cpu.usage });
    }

    if (this.currentMetrics.memory.used / this.currentMetrics.memory.total * 100 > thresholds.memory) {
      this.emit('threshold-exceeded', { type: 'memory', value: this.currentMetrics.memory.used });
    }
  }

  public getCurrentMetrics(): PerformanceMetrics {
    return { ...this.currentMetrics };
  }

  public getSettings(): PerformanceSettings {
    return { ...this.settings };
  }

  public updateSettings(newSettings: Partial<PerformanceSettings>): void {
    this.settings = { ...this.settings, ...newSettings };
    this.emit('settings-updated', this.settings);
  }
}

export const performanceManager = PerformanceManager.getInstance();
