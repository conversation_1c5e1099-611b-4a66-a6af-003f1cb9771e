import { EventEmitter } from 'events';
import { promises as fs } from 'fs';
import path from 'path';

import { BrowserWindow, app, session } from 'electron';

export interface PerformanceConfig {
  enableHardwareAcceleration: boolean;
  enableGpuAcceleration: boolean;
  enableWebGL: boolean;
  enableWebGL2: boolean;
  enableWebGPU: boolean;
  enableWebAssembly: boolean;
  enableSharedArrayBuffer: boolean;
  enableWebWorkers: boolean;
  enableServiceWorkers: boolean;
  enableBackgroundThrottling: boolean;
  enableBackgroundHibernation: boolean;
  maxMemoryUsage: number;
  maxConcurrentDownloads: number;
  maxConcurrentUploads: number;
  cacheSize: number;
  diskCacheSize: number;
  memoryCacheSize: number;
}

export class PerformanceManager extends EventEmitter {
  private static instance: PerformanceManager;
  private config: PerformanceConfig;
  private performanceMetrics: Map<string, number>;
  private resourceUsage: Map<string, number>;

  private constructor() {
    super();
    this.config = this.getDefaultConfig();
    this.performanceMetrics = new Map();
    this.resourceUsage = new Map();
    this.initializePerformance();
  }

  public static getInstance(): PerformanceManager {
    if (!PerformanceManager.instance) {
      PerformanceManager.instance = new PerformanceManager();
    }
    return PerformanceManager.instance;
  }

  private getDefaultConfig(): PerformanceConfig {
    return {
      enableHardwareAcceleration: true,
      enableGpuAcceleration: true,
      enableWebGL: true,
      enableWebGL2: true,
      enableWebGPU: true,
      enableWebAssembly: true,
      enableSharedArrayBuffer: true,
      enableWebWorkers: true,
      enableServiceWorkers: true,
      enableBackgroundThrottling: true,
      enableBackgroundHibernation: true,
      maxMemoryUsage: 2048, // MB
      maxConcurrentDownloads: 6,
      maxConcurrentUploads: 4,
      cacheSize: 1024, // MB
      diskCacheSize: 512, // MB
      memoryCacheSize: 256, // MB
    };
  }

  private async initializePerformance(): Promise<void> {
    try {
      await this.setupHardwareAcceleration();
      await this.setupCache();
      await this.setupResourceLimits();
      this.setupPerformanceMonitoring();
    } catch (error) {
      console.error('Performance initialization failed:', error);
      this.emit('performance-error', error);
    }
  }

  private async setupHardwareAcceleration(): Promise<void> {
    if (this.config.enableHardwareAcceleration) {
      app.commandLine.appendSwitch('enable-gpu-rasterization');
      app.commandLine.appendSwitch('enable-zero-copy');
      app.commandLine.appendSwitch('ignore-gpu-blocklist');
      app.commandLine.appendSwitch('enable-gpu-compositing');
    }

    if (this.config.enableGpuAcceleration) {
      app.commandLine.appendSwitch('enable-gpu');
      app.commandLine.appendSwitch('enable-gpu-sandbox');
    }
  }

  private async setupCache(): Promise<void> {
    const cachePath = path.join(app.getPath('userData'), 'Cache');
    await fs.mkdir(cachePath, { recursive: true });

    session.defaultSession.setCacheSize(this.config.cacheSize * 1024 * 1024);
    session.defaultSession.setDiskCacheSize(this.config.diskCacheSize * 1024 * 1024);
    session.defaultSession.setMemoryCacheSize(this.config.memoryCacheSize * 1024 * 1024);
  }

  private async setupResourceLimits(): Promise<void> {
    app.commandLine.appendSwitch('js-flags', `--max-old-space-size=${this.config.maxMemoryUsage}`);
    app.commandLine.appendSwitch(
      'max-concurrent-downloads',
      this.config.maxConcurrentDownloads.toString()
    );
    app.commandLine.appendSwitch(
      'max-concurrent-uploads',
      this.config.maxConcurrentUploads.toString()
    );
  }

  private setupPerformanceMonitoring(): void {
    setInterval(() => {
      this.collectPerformanceMetrics();
    }, 5000);

    app.on('browser-window-created', (event, window) => {
      this.monitorWindowPerformance(window);
    });
  }

  private async collectPerformanceMetrics(): Promise<void> {
    const metrics = await this.getSystemMetrics();
    this.performanceMetrics = new Map(Object.entries(metrics));
    this.emit('metrics-updated', this.performanceMetrics);
  }

  private async getSystemMetrics(): Promise<Record<string, number>> {
    const metrics: Record<string, number> = {
      cpuUsage: process.cpuUsage().user / 1000000,
      memoryUsage: process.memoryUsage().heapUsed / 1024 / 1024,
      uptime: process.uptime(),
    };

    return metrics;
  }

  private monitorWindowPerformance(window: BrowserWindow): void {
    window.webContents.on('did-finish-load', () => {
      this.measurePageLoadPerformance(window);
    });

    window.webContents.on('did-start-loading', () => {
      this.emit('page-load-start', window.id);
    });

    window.webContents.on('did-stop-loading', () => {
      this.emit('page-load-end', window.id);
    });
  }

  private async measurePageLoadPerformance(window: BrowserWindow): Promise<void> {
    const metrics = await window.webContents.executeJavaScript(`
      {
        navigationStart: performance.timing.navigationStart,
        loadEventEnd: performance.timing.loadEventEnd,
        domComplete: performance.timing.domComplete,
        domInteractive: performance.timing.domInteractive,
        firstPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-paint')?.startTime,
        firstContentfulPaint: performance.getEntriesByType('paint').find(entry => entry.name === 'first-contentful-paint')?.startTime
      }
    `);

    this.emit('page-performance', {
      windowId: window.id,
      metrics,
    });
  }

  public getPerformanceConfig(): PerformanceConfig {
    return { ...this.config };
  }

  public updatePerformanceConfig(newConfig: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.emit('config-updated', this.config);
  }

  public getPerformanceMetrics(): Map<string, number> {
    return new Map(this.performanceMetrics);
  }

  public getResourceUsage(): Map<string, number> {
    return new Map(this.resourceUsage);
  }

  public async optimizePerformance(): Promise<void> {
    try {
      await this.clearCache();
      await this.defragmentCache();
      await this.optimizeMemory();
      this.emit('optimization-complete');
    } catch (error) {
      console.error('Performance optimization failed:', error);
      this.emit('optimization-error', error);
    }
  }

  private async clearCache(): Promise<void> {
    await session.defaultSession.clearCache();
  }

  private async defragmentCache(): Promise<void> {
    // Implement cache defragmentation logic
  }

  private async optimizeMemory(): Promise<void> {
    if (global.gc) {
      global.gc();
    }
  }
}
