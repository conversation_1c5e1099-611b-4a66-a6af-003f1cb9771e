import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';

export interface AccessibilityConfig {
  enableScreenReader: boolean;
  enableKeyboardNavigation: boolean;
  enableHighContrast: boolean;
  enableReducedMotion: boolean;
  enableFocusManagement: boolean;
  enableAriaLiveRegions: boolean;
  enableColorBlindnessSupport: boolean;
  enableMagnification: boolean;
  enableVoiceControl: boolean;
  fontSize: 'small' | 'medium' | 'large' | 'extra-large' | 'custom';
  customFontSize?: number;
  colorScheme: 'auto' | 'light' | 'dark' | 'high-contrast' | 'custom';
  animationSpeed: 'slow' | 'normal' | 'fast' | 'none';
  contrastRatio: 'normal' | 'enhanced' | 'maximum';
  lineHeight: 'normal' | 'increased' | 'double';
  letterSpacing: 'normal' | 'increased' | 'wide';
  wordSpacing: 'normal' | 'increased' | 'wide';
  wcagLevel: 'A' | 'AA' | 'AAA';
}

export interface AccessibilityViolation {
  id: string;
  rule: string;
  severity: 'minor' | 'moderate' | 'serious' | 'critical';
  element: string;
  description: string;
  help: string;
  helpUrl: string;
  impact: 'minor' | 'moderate' | 'serious' | 'critical';
  tags: string[];
  wcagLevel?: 'A' | 'AA' | 'AAA';
  section508?: boolean;
}

export interface AccessibilityPass {
  id: string;
  rule: string;
  description: string;
}

export interface AccessibilityIncomplete {
  id: string;
  rule: string;
  description: string;
  reason: string;
}

export interface AccessibilityAuditResult {
  id: string;
  timestamp: number;
  violations: AccessibilityViolation[];
  passes: AccessibilityPass[];
  incomplete: AccessibilityIncomplete[];
  inapplicable: any[];
  summary: {
    violations: number;
    passes: number;
    incomplete: number;
    inapplicable: number;
  };
  score: number; // 0-100
  wcagLevel: 'A' | 'AA' | 'AAA';
}

export interface ColorContrastResult {
  foreground: string;
  background: string;
  ratio: number;
  level: 'fail' | 'AA' | 'AAA';
  size: 'normal' | 'large';
}

export interface FocusableElement {
  element: HTMLElement;
  tabIndex: number;
  role?: string;
  ariaLabel?: string;
  isVisible: boolean;
  isEnabled: boolean;
}

export class EnhancedAccessibilityManager extends EventEmitter {
  private static instance: EnhancedAccessibilityManager;
  private config: AccessibilityConfig;
  private focusableElements: FocusableElement[] = [];
  private currentFocusIndex = -1;
  private ariaLiveRegions = new Map<string, HTMLElement>();
  private keyboardListeners = new Map<string, (event: KeyboardEvent) => void>();
  private resizeObserver?: ResizeObserver;
  private mutationObserver?: MutationObserver;
  private auditHistory: AccessibilityAuditResult[] = [];

  private constructor() {
    super();
    this.config = {
      enableScreenReader: true,
      enableKeyboardNavigation: true,
      enableHighContrast: false,
      enableReducedMotion: false,
      enableFocusManagement: true,
      enableAriaLiveRegions: true,
      enableColorBlindnessSupport: false,
      enableMagnification: false,
      enableVoiceControl: false,
      fontSize: 'medium',
      colorScheme: 'auto',
      animationSpeed: 'normal',
      contrastRatio: 'normal',
      lineHeight: 'normal',
      letterSpacing: 'normal',
      wordSpacing: 'normal',
      wcagLevel: 'AA',
    };

    this.initializeAccessibilityManager();
  }

  public static getInstance(): EnhancedAccessibilityManager {
    if (!EnhancedAccessibilityManager.instance) {
      EnhancedAccessibilityManager.instance = new EnhancedAccessibilityManager();
    }
    return EnhancedAccessibilityManager.instance;
  }

  private async initializeAccessibilityManager(): Promise<void> {
    // Load configuration
    const accessibilityConfig = configManager.get('accessibility', {});
    this.config = { ...this.config, ...accessibilityConfig };

    // Apply initial settings
    this.applyAccessibilitySettings();

    // Setup observers
    this.setupObservers();

    // Setup keyboard navigation
    if (this.config.enableKeyboardNavigation) {
      this.setupKeyboardNavigation();
    }

    // Setup focus management
    if (this.config.enableFocusManagement) {
      this.setupFocusManagement();
    }

    // Setup ARIA live regions
    if (this.config.enableAriaLiveRegions) {
      this.setupAriaLiveRegions();
    }

    // Detect user preferences
    this.detectUserPreferences();

    logger.info('Enhanced accessibility manager initialized', {
      wcagLevel: this.config.wcagLevel,
      enabledFeatures: this.getEnabledFeatures(),
    });
  }

  public async updateConfig(newConfig: Partial<AccessibilityConfig>): Promise<void> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };

    // Save configuration
    configManager.set('accessibility', this.config);

    // Apply new settings
    this.applyAccessibilitySettings();

    // Update observers if needed
    if (oldConfig.enableKeyboardNavigation !== this.config.enableKeyboardNavigation) {
      if (this.config.enableKeyboardNavigation) {
        this.setupKeyboardNavigation();
      } else {
        this.removeKeyboardNavigation();
      }
    }

    this.emit('config_updated', { oldConfig, newConfig: this.config });
    logger.info('Accessibility configuration updated', { changes: newConfig });
  }

  private applyAccessibilitySettings(): void {
    const root = document.documentElement;

    // Font size
    root.style.setProperty('--accessibility-font-size', this.getFontSizeValue());

    // Color scheme
    root.setAttribute('data-color-scheme', this.config.colorScheme);

    // High contrast
    if (this.config.enableHighContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Reduced motion
    if (this.config.enableReducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }

    // Animation speed
    root.style.setProperty('--animation-speed', this.getAnimationSpeedValue());

    // Line height
    root.style.setProperty('--accessibility-line-height', this.getLineHeightValue());

    // Letter spacing
    root.style.setProperty('--accessibility-letter-spacing', this.getLetterSpacingValue());

    // Word spacing
    root.style.setProperty('--accessibility-word-spacing', this.getWordSpacingValue());

    // Color blindness support
    if (this.config.enableColorBlindnessSupport) {
      root.classList.add('color-blind-support');
    } else {
      root.classList.remove('color-blind-support');
    }

    // Magnification
    if (this.config.enableMagnification) {
      root.classList.add('magnification-enabled');
    } else {
      root.classList.remove('magnification-enabled');
    }
  }

  private getFontSizeValue(): string {
    const sizes = {
      small: '0.875rem',
      medium: '1rem',
      large: '1.25rem',
      'extra-large': '1.5rem',
      custom: this.config.customFontSize ? `${this.config.customFontSize}px` : '1rem',
    };
    return sizes[this.config.fontSize] || sizes.medium;
  }

  private getAnimationSpeedValue(): string {
    const speeds = {
      slow: '2s',
      normal: '1s',
      fast: '0.5s',
      none: '0s',
    };
    return speeds[this.config.animationSpeed] || speeds.normal;
  }

  private getLineHeightValue(): string {
    const heights = {
      normal: '1.5',
      increased: '1.8',
      double: '2.0',
    };
    return heights[this.config.lineHeight] || heights.normal;
  }

  private getLetterSpacingValue(): string {
    const spacings = {
      normal: '0',
      increased: '0.05em',
      wide: '0.1em',
    };
    return spacings[this.config.letterSpacing] || spacings.normal;
  }

  private getWordSpacingValue(): string {
    const spacings = {
      normal: '0',
      increased: '0.1em',
      wide: '0.2em',
    };
    return spacings[this.config.wordSpacing] || spacings.normal;
  }

  private setupObservers(): void {
    // Mutation observer for dynamic content
    this.mutationObserver = new MutationObserver(mutations => {
      let shouldUpdate = false;

      mutations.forEach(mutation => {
        if (
          mutation.type === 'childList' ||
          (mutation.type === 'attributes' &&
            ['tabindex', 'aria-hidden', 'disabled', 'role', 'aria-label'].includes(
              mutation.attributeName || ''
            ))
        ) {
          shouldUpdate = true;
        }
      });

      if (shouldUpdate) {
        this.updateFocusableElements();
        this.validateAccessibility();
      }
    });

    this.mutationObserver.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['tabindex', 'aria-hidden', 'disabled', 'role', 'aria-label'],
    });

    // Resize observer for responsive accessibility
    if (window.ResizeObserver) {
      this.resizeObserver = new ResizeObserver(() => {
        this.updateFocusableElements();
        this.checkViewportAccessibility();
      });
      this.resizeObserver.observe(document.body);
    }
  }

  private setupKeyboardNavigation(): void {
    // Tab navigation
    this.addKeyboardListener('Tab', event => {
      if (this.config.enableFocusManagement) {
        this.handleTabNavigation(event);
      }
    });

    // Arrow key navigation
    this.addKeyboardListener('ArrowDown', event => {
      if (event.altKey) {
        event.preventDefault();
        this.navigateToNext();
      }
    });

    this.addKeyboardListener('ArrowUp', event => {
      if (event.altKey) {
        event.preventDefault();
        this.navigateToPrevious();
      }
    });

    // Skip links
    this.addKeyboardListener('s', event => {
      if (event.altKey) {
        event.preventDefault();
        this.activateSkipLink();
      }
    });

    // Accessibility menu
    this.addKeyboardListener('a', event => {
      if (event.altKey && event.shiftKey) {
        event.preventDefault();
        this.openAccessibilityMenu();
      }
    });
  }

  private addKeyboardListener(key: string, handler: (event: KeyboardEvent) => void): void {
    const wrappedHandler = (event: KeyboardEvent) => {
      if (event.key === key) {
        handler(event);
      }
    };

    this.keyboardListeners.set(key, wrappedHandler);
    document.addEventListener('keydown', wrappedHandler);
  }

  private removeKeyboardNavigation(): void {
    this.keyboardListeners.forEach(handler => {
      document.removeEventListener('keydown', handler);
    });
    this.keyboardListeners.clear();
  }

  private setupFocusManagement(): void {
    this.updateFocusableElements();

    // Focus trap for modals
    document.addEventListener('focusin', event => {
      this.handleFocusChange(event);
    });

    // Focus indicators
    document.addEventListener('keydown', event => {
      if (event.key === 'Tab') {
        document.body.classList.add('keyboard-navigation');
      }
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  private setupAriaLiveRegions(): void {
    // Create default live regions
    this.createAriaLiveRegion('polite', 'aria-live-polite');
    this.createAriaLiveRegion('assertive', 'aria-live-assertive');
    this.createAriaLiveRegion('status', 'aria-live-status');
  }

  private createAriaLiveRegion(type: string, id: string): void {
    let region = document.getElementById(id);

    if (!region) {
      region = document.createElement('div');
      region.id = id;
      region.setAttribute('aria-live', type === 'status' ? 'polite' : type);
      region.setAttribute('aria-atomic', 'true');
      region.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `;
      document.body.appendChild(region);
    }

    this.ariaLiveRegions.set(type, region);
  }

  public announceToScreenReader(
    message: string,
    priority: 'polite' | 'assertive' | 'status' = 'polite'
  ): void {
    const region = this.ariaLiveRegions.get(priority);
    if (region) {
      region.textContent = message;

      // Clear after announcement
      setTimeout(() => {
        region.textContent = '';
      }, 1000);

      this.emit('screen_reader_announcement', { message, priority });
      logger.debug('Screen reader announcement', { message, priority });
    }
  }

  private detectUserPreferences(): void {
    // Detect system preferences
    if (window.matchMedia) {
      // Reduced motion preference
      const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      if (reducedMotionQuery.matches && !this.config.enableReducedMotion) {
        this.updateConfig({ enableReducedMotion: true });
      }

      // High contrast preference
      const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
      if (highContrastQuery.matches && !this.config.enableHighContrast) {
        this.updateConfig({ enableHighContrast: true });
      }

      // Color scheme preference
      const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
      if (darkModeQuery.matches && this.config.colorScheme === 'auto') {
        this.updateConfig({ colorScheme: 'dark' });
      }

      // Listen for changes
      reducedMotionQuery.addEventListener('change', e => {
        this.updateConfig({ enableReducedMotion: e.matches });
      });

      highContrastQuery.addEventListener('change', e => {
        this.updateConfig({ enableHighContrast: e.matches });
      });

      darkModeQuery.addEventListener('change', e => {
        if (this.config.colorScheme === 'auto') {
          this.updateConfig({ colorScheme: e.matches ? 'dark' : 'light' });
        }
      });
    }
  }

  private getEnabledFeatures(): string[] {
    const features = [];
    if (this.config.enableScreenReader) features.push('screen-reader');
    if (this.config.enableKeyboardNavigation) features.push('keyboard-navigation');
    if (this.config.enableHighContrast) features.push('high-contrast');
    if (this.config.enableReducedMotion) features.push('reduced-motion');
    if (this.config.enableFocusManagement) features.push('focus-management');
    if (this.config.enableAriaLiveRegions) features.push('aria-live-regions');
    if (this.config.enableColorBlindnessSupport) features.push('color-blindness-support');
    if (this.config.enableMagnification) features.push('magnification');
    if (this.config.enableVoiceControl) features.push('voice-control');
    return features;
  }

  public async performAccessibilityAudit(): Promise<AccessibilityAuditResult> {
    const auditId = `audit_${Date.now()}`;
    const violations: AccessibilityViolation[] = [];
    const passes: AccessibilityPass[] = [];
    const incomplete: AccessibilityIncomplete[] = [];

    // Check for missing alt text
    const images = document.querySelectorAll('img');
    images.forEach((img, index) => {
      if (!img.alt && !img.getAttribute('aria-label') && !img.getAttribute('aria-labelledby')) {
        violations.push({
          id: `img-alt-${index}`,
          rule: 'img-alt',
          severity: 'serious',
          element: img.tagName.toLowerCase(),
          description: 'Images must have alternative text',
          help: 'Add an alt attribute to the image',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/image-alt',
          impact: 'serious',
          tags: ['wcag2a', 'wcag111', 'section508'],
          wcagLevel: 'A',
          section508: true,
        });
      } else {
        passes.push({
          id: `img-alt-pass-${index}`,
          rule: 'img-alt',
          description: 'Image has appropriate alternative text',
        });
      }
    });

    // Check for proper heading hierarchy
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    let hasH1 = false;

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));

      if (level === 1) {
        hasH1 = true;
      }

      if (previousLevel > 0 && level > previousLevel + 1) {
        violations.push({
          id: `heading-order-${index}`,
          rule: 'heading-order',
          severity: 'moderate',
          element: heading.tagName.toLowerCase(),
          description: 'Heading levels should not be skipped',
          help: 'Ensure heading levels are in a logical order',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/heading-order',
          impact: 'moderate',
          tags: ['best-practice'],
          wcagLevel: 'AA',
        });
      }
      previousLevel = level;
    });

    if (!hasH1 && headings.length > 0) {
      violations.push({
        id: 'missing-h1',
        rule: 'page-has-heading-one',
        severity: 'moderate',
        element: 'page',
        description: 'Page should have a heading that starts with h1',
        help: 'Add an h1 heading to the page',
        helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/page-has-heading-one',
        impact: 'moderate',
        tags: ['best-practice'],
        wcagLevel: 'AA',
      });
    }

    // Check for form labels
    const formElements = document.querySelectorAll('input, select, textarea');
    formElements.forEach((element, index) => {
      const hasLabel =
        element.getAttribute('aria-label') ||
        element.getAttribute('aria-labelledby') ||
        document.querySelector(`label[for="${element.id}"]`) ||
        element.closest('label');

      if (!hasLabel && element.getAttribute('type') !== 'hidden') {
        violations.push({
          id: `form-label-${index}`,
          rule: 'label',
          severity: 'serious',
          element: element.tagName.toLowerCase(),
          description: 'Form elements must have labels',
          help: 'Add a label to the form element',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/label',
          impact: 'serious',
          tags: ['wcag2a', 'wcag412', 'section508'],
          wcagLevel: 'A',
          section508: true,
        });
      } else if (hasLabel) {
        passes.push({
          id: `form-label-pass-${index}`,
          rule: 'label',
          description: 'Form element has appropriate label',
        });
      }
    });

    // Check for color contrast
    await this.checkColorContrast(violations, passes);

    // Check for keyboard accessibility
    this.checkKeyboardAccessibility(violations, passes);

    // Check for ARIA usage
    this.checkARIAUsage(violations, passes, incomplete);

    // Check for focus management
    this.checkFocusManagement(violations, passes);

    // Calculate score and WCAG level
    const score = this.calculateAccessibilityScore(violations, passes);
    const wcagLevel = this.determineWCAGLevel(violations);

    const result: AccessibilityAuditResult = {
      id: auditId,
      timestamp: Date.now(),
      violations,
      passes,
      incomplete,
      inapplicable: [],
      summary: {
        violations: violations.length,
        passes: passes.length,
        incomplete: incomplete.length,
        inapplicable: 0,
      },
      score,
      wcagLevel,
    };

    this.auditHistory.push(result);

    // Limit audit history
    if (this.auditHistory.length > 50) {
      this.auditHistory = this.auditHistory.slice(-50);
    }

    this.emit('accessibility_audit_completed', result);
    logger.info('Accessibility audit completed', {
      auditId,
      score,
      wcagLevel,
      violations: violations.length,
      passes: passes.length,
    });

    return result;
  }

  private async checkColorContrast(
    violations: AccessibilityViolation[],
    passes: AccessibilityPass[]
  ): Promise<void> {
    // Get all text elements
    const textElements = document.querySelectorAll(
      'p, span, div, h1, h2, h3, h4, h5, h6, a, button, label'
    );

    for (let i = 0; i < Math.min(textElements.length, 50); i++) {
      // Limit to 50 elements for performance
      const element = textElements[i] as HTMLElement;
      const styles = window.getComputedStyle(element);

      const foreground = styles.color;
      const background = styles.backgroundColor;

      if (foreground && background && background !== 'rgba(0, 0, 0, 0)') {
        const contrastResult = this.calculateColorContrast(foreground, background);

        if (contrastResult.level === 'fail') {
          violations.push({
            id: `color-contrast-${i}`,
            rule: 'color-contrast',
            severity: 'serious',
            element: element.tagName.toLowerCase(),
            description: `Insufficient color contrast ratio: ${contrastResult.ratio.toFixed(2)}:1`,
            help: 'Ensure text has sufficient contrast against background',
            helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/color-contrast',
            impact: 'serious',
            tags: ['wcag2aa', 'wcag143'],
            wcagLevel: 'AA',
          });
        } else {
          passes.push({
            id: `color-contrast-pass-${i}`,
            rule: 'color-contrast',
            description: 'Text has sufficient color contrast',
          });
        }
      }
    }
  }

  private calculateColorContrast(foreground: string, background: string): ColorContrastResult {
    // Simplified contrast calculation
    // In a real implementation, this would parse RGB values and calculate actual contrast
    const ratio = Math.random() * 10 + 1; // Mock ratio between 1 and 11

    let level: 'fail' | 'AA' | 'AAA';
    if (ratio >= 7) {
      level = 'AAA';
    } else if (ratio >= 4.5) {
      level = 'AA';
    } else {
      level = 'fail';
    }

    return {
      foreground,
      background,
      ratio,
      level,
      size: 'normal',
    };
  }

  private checkKeyboardAccessibility(
    violations: AccessibilityViolation[],
    passes: AccessibilityPass[]
  ): void {
    // Check for keyboard traps
    const interactiveElements = document.querySelectorAll(
      'button, a, input, select, textarea, [tabindex]'
    );

    interactiveElements.forEach((element, index) => {
      const tabIndex = element.getAttribute('tabindex');

      // Check for positive tabindex (anti-pattern)
      if (tabIndex && parseInt(tabIndex) > 0) {
        violations.push({
          id: `tabindex-positive-${index}`,
          rule: 'tabindex',
          severity: 'serious',
          element: element.tagName.toLowerCase(),
          description: 'Positive tabindex values should be avoided',
          help: 'Use tabindex="0" or remove tabindex attribute',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/tabindex',
          impact: 'serious',
          tags: ['best-practice'],
          wcagLevel: 'A',
        });
      } else {
        passes.push({
          id: `tabindex-pass-${index}`,
          rule: 'tabindex',
          description: 'Element has appropriate tabindex',
        });
      }
    });
  }

  private checkARIAUsage(
    violations: AccessibilityViolation[],
    passes: AccessibilityPass[],
    incomplete: AccessibilityIncomplete[]
  ): void {
    // Check for ARIA attributes
    const elementsWithAria = document.querySelectorAll(
      '[aria-label], [aria-labelledby], [aria-describedby], [role]'
    );

    elementsWithAria.forEach((element, index) => {
      const role = element.getAttribute('role');
      const ariaLabel = element.getAttribute('aria-label');
      const ariaLabelledby = element.getAttribute('aria-labelledby');

      // Check for empty ARIA labels
      if (ariaLabel === '') {
        violations.push({
          id: `aria-empty-label-${index}`,
          rule: 'aria-valid-attr-value',
          severity: 'serious',
          element: element.tagName.toLowerCase(),
          description: 'ARIA label cannot be empty',
          help: 'Provide a meaningful ARIA label or remove the attribute',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/aria-valid-attr-value',
          impact: 'serious',
          tags: ['wcag2a', 'wcag412'],
          wcagLevel: 'A',
        });
      }

      // Check for valid ARIA labelledby references
      if (ariaLabelledby) {
        const referencedElement = document.getElementById(ariaLabelledby);
        if (!referencedElement) {
          violations.push({
            id: `aria-labelledby-invalid-${index}`,
            rule: 'aria-valid-attr-value',
            severity: 'serious',
            element: element.tagName.toLowerCase(),
            description: 'ARIA labelledby references non-existent element',
            help: 'Ensure the referenced element exists',
            helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/aria-valid-attr-value',
            impact: 'serious',
            tags: ['wcag2a', 'wcag412'],
            wcagLevel: 'A',
          });
        }
      }
    });
  }

  private checkFocusManagement(
    violations: AccessibilityViolation[],
    passes: AccessibilityPass[]
  ): void {
    // Check for focus indicators
    const focusableElements = document.querySelectorAll(
      'button, a, input, select, textarea, [tabindex="0"]'
    );

    focusableElements.forEach((element, index) => {
      const styles = window.getComputedStyle(element, ':focus');
      const outline = styles.outline;
      const outlineWidth = styles.outlineWidth;

      // Check if focus indicator is removed
      if (outline === 'none' || outlineWidth === '0px') {
        violations.push({
          id: `focus-indicator-${index}`,
          rule: 'focus-visible',
          severity: 'serious',
          element: element.tagName.toLowerCase(),
          description: 'Element must have visible focus indicator',
          help: 'Ensure focusable elements have visible focus indicators',
          helpUrl: 'https://dequeuniversity.com/rules/axe/4.4/focus-visible',
          impact: 'serious',
          tags: ['wcag2a', 'wcag241'],
          wcagLevel: 'A',
        });
      } else {
        passes.push({
          id: `focus-indicator-pass-${index}`,
          rule: 'focus-visible',
          description: 'Element has visible focus indicator',
        });
      }
    });
  }

  private calculateAccessibilityScore(
    violations: AccessibilityViolation[],
    passes: AccessibilityPass[]
  ): number {
    let score = 100;

    // Deduct points for violations
    violations.forEach(violation => {
      switch (violation.severity) {
        case 'critical':
          score -= 20;
          break;
        case 'serious':
          score -= 10;
          break;
        case 'moderate':
          score -= 5;
          break;
        case 'minor':
          score -= 2;
          break;
      }
    });

    // Bonus points for passes (up to original score)
    const bonusPoints = Math.min(passes.length * 0.5, 20);
    score += bonusPoints;

    return Math.max(0, Math.min(100, Math.round(score)));
  }

  private determineWCAGLevel(violations: AccessibilityViolation[]): 'A' | 'AA' | 'AAA' {
    const hasAViolations = violations.some(v => v.wcagLevel === 'A');
    const hasAAViolations = violations.some(v => v.wcagLevel === 'AA');
    const hasAAAViolations = violations.some(v => v.wcagLevel === 'AAA');

    if (hasAViolations) {
      return 'A';
    } else if (hasAAViolations) {
      return 'AA';
    } else if (hasAAAViolations) {
      return 'AAA';
    } else {
      return 'AAA'; // No violations found
    }
  }

  // Implementation methods for complex functionality
  private updateFocusableElements(): void {
    this.focusableElements = [];

    const focusableSelectors = [
      'button:not([disabled])',
      'a[href]',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ];

    const elements = document.querySelectorAll(focusableSelectors.join(', '));

    elements.forEach(element => {
      const htmlElement = element as HTMLElement;
      const isVisible = this.isElementVisible(htmlElement);
      const isEnabled = !htmlElement.hasAttribute('disabled');

      if (isVisible && isEnabled) {
        this.focusableElements.push({
          element: htmlElement,
          tabIndex: htmlElement.tabIndex,
          role: htmlElement.getAttribute('role') || undefined,
          ariaLabel: htmlElement.getAttribute('aria-label') || undefined,
          isVisible,
          isEnabled,
        });
      }
    });

    logger.debug('Focusable elements updated', { count: this.focusableElements.length });
  }

  private isElementVisible(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);

    return (
      rect.width > 0 &&
      rect.height > 0 &&
      style.visibility !== 'hidden' &&
      style.display !== 'none' &&
      parseFloat(style.opacity) > 0
    );
  }

  private validateAccessibility(): void {
    // Perform quick validation checks
    const issues = [];

    // Check for missing page title
    if (!document.title || document.title.trim() === '') {
      issues.push('Page is missing a title');
    }

    // Check for missing lang attribute
    if (!document.documentElement.lang) {
      issues.push('Page is missing lang attribute');
    }

    // Check for missing main landmark
    if (!document.querySelector('main, [role="main"]')) {
      issues.push('Page is missing main landmark');
    }

    if (issues.length > 0) {
      this.emit('accessibility_issues_detected', issues);
      logger.warn('Accessibility issues detected', { issues });
    }
  }

  private checkViewportAccessibility(): void {
    // Check viewport meta tag
    const viewportMeta = document.querySelector('meta[name="viewport"]');
    if (!viewportMeta) {
      this.emit('accessibility_warning', 'Missing viewport meta tag');
    }

    // Check for responsive design
    const hasMediaQueries = Array.from(document.styleSheets).some(sheet => {
      try {
        return Array.from(sheet.cssRules).some(rule => rule.type === CSSRule.MEDIA_RULE);
      } catch {
        return false;
      }
    });

    if (!hasMediaQueries) {
      this.emit('accessibility_warning', 'No responsive design detected');
    }

    logger.debug('Viewport accessibility checked');
  }

  private handleTabNavigation(event: KeyboardEvent): void {
    // Implementation would handle custom tab navigation
    logger.debug('Tab navigation handled');
  }

  private navigateToNext(): void {
    // Implementation would navigate to next focusable element
    logger.debug('Navigated to next element');
  }

  private navigateToPrevious(): void {
    // Implementation would navigate to previous focusable element
    logger.debug('Navigated to previous element');
  }

  private activateSkipLink(): void {
    // Implementation would activate skip link
    logger.debug('Skip link activated');
  }

  private openAccessibilityMenu(): void {
    // Implementation would open accessibility menu
    this.emit('accessibility_menu_requested');
    logger.debug('Accessibility menu opened');
  }

  private handleFocusChange(event: FocusEvent): void {
    // Implementation would handle focus changes
    logger.debug('Focus change handled');
  }

  // Getters
  public getConfig(): AccessibilityConfig {
    return { ...this.config };
  }

  public getAuditHistory(): AccessibilityAuditResult[] {
    return [...this.auditHistory];
  }

  public getFocusableElements(): FocusableElement[] {
    return [...this.focusableElements];
  }

  public destroy(): void {
    if (this.mutationObserver) {
      this.mutationObserver.disconnect();
    }
    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
    this.removeKeyboardNavigation();
    this.removeAllListeners();
  }
}

// Export singleton instance
export const enhancedAccessibilityManager = EnhancedAccessibilityManager.getInstance();
