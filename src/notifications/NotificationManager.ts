import { join } from 'path';

import { BrowserWindow, Notification, WebContents, app } from 'electron';

import type {
  NotificationAccessibility,
  NotificationAnimation,
  NotificationBehavior,
  NotificationLayout,
  NotificationPosition,
  NotificationPriority,
  NotificationSound,
  NotificationTheme,
  NotificationType,
} from '../types/notifications';
import { errorManager } from '../core/ErrorManager';
import { logger } from '../logging/Logger';

interface NotificationConfig {
  enabled: boolean;
  sound: boolean;
  vibration: boolean;
  position: NotificationPosition;
  duration: number;
  maxVisible: number;
  grouping: boolean;
  priority: NotificationPriority;
  actions: boolean;
  history: boolean;
  historyLimit: number;
  theme?: NotificationTheme;
  animation?: NotificationAnimation;
  behavior?: NotificationBehavior;
  layout?: NotificationLayout;
  accessibility?: NotificationAccessibility;
}

interface NotificationAction {
  type: 'button';
  label: string;
  callback?: () => void;
}

interface NotificationOptions {
  title: string;
  body: string;
  icon?: string;
  silent?: boolean;
  urgency?: 'low' | 'normal' | 'critical';
  actions?: NotificationAction[];
  timeout?: number;
  onClick?: () => void;
  onClose?: () => void;
  data?: Record<string, any>;
  group?: string;
  tag?: string;
  theme?: NotificationTheme;
  animation?: NotificationAnimation;
  behavior?: NotificationBehavior;
  layout?: NotificationLayout;
  accessibility?: NotificationAccessibility;
}

interface NotificationWithHandlers extends Notification {
  onClick?: () => void;
  onClose?: () => void;
  tag?: string;
}

class NotificationManager {
  private static instance: NotificationManager;
  private config: NotificationConfig;
  private history: Array<NotificationOptions & { timestamp: number }>;
  private activeNotifications: Map<string, NotificationWithHandlers>;
  private readonly defaultConfig: NotificationConfig = {
    enabled: true,
    sound: true,
    vibration: true,
    position: 'top-right',
    duration: 5000,
    maxVisible: 5,
    grouping: true,
    priority: 'normal',
    actions: true,
    history: true,
    historyLimit: 100,
    theme: {
      background: '#ffffff',
      text: '#000000',
      border: '#e0e0e0',
      shadow: '0 2px 4px rgba(0,0,0,0.1)',
      borderRadius: 8,
      icon: {
        color: '#2196f3',
        background: '#ffffff',
        size: 24,
        borderRadius: 4,
      },
    },
    animation: {
      initial: { opacity: 0, y: -20 },
      animate: { opacity: 1, y: 0 },
      exit: { opacity: 0, y: -20 },
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 30,
      },
    },
    behavior: 'stack',
    layout: 'stack',
    accessibility: 'default',
  };

  private constructor() {
    this.config = { ...this.defaultConfig };
    this.history = [];
    this.activeNotifications = new Map();
    this.initialize();
  }

  public static getInstance(): NotificationManager {
    if (!NotificationManager.instance) {
      NotificationManager.instance = new NotificationManager();
    }
    return NotificationManager.instance;
  }

  private initialize(): void {
    try {
      if (!Notification.isSupported()) {
        logger.warn('Notifications are not supported on this system');
        this.config.enabled = false;
        return;
      }

      // Set up notification click handler
      app.on('browser-window-created', (event, window) => {
        const webContents = window.webContents;

        // Handle notification clicks
        webContents.on(
          'notification-click' as any,
          (event: Electron.Event, notification: NotificationWithHandlers) => {
            const options = this.activeNotifications.get(notification.tag || '');
            if (options?.onClick) {
              options.onClick();
            }
          }
        );

        // Handle notification closes
        webContents.on(
          'notification-close' as any,
          (event: Electron.Event, notification: NotificationWithHandlers) => {
            const options = this.activeNotifications.get(notification.tag || '');
            if (options?.onClose) {
              options.onClose();
            }
            this.activeNotifications.delete(notification.tag || '');
          }
        );
      });
    } catch (error) {
      trackError(error as Error, {
        context: { component: 'NotificationManager' },
      });
    }
  }

  public show(options: NotificationOptions): void {
    if (!this.config.enabled) return;

    try {
      const notification = new Notification({
        title: options.title,
        body: options.body,
        icon: options.icon || join(app.getPath('userData'), 'icons', 'notification.png'),
        silent: options.silent || !this.config.sound,
        urgency: options.urgency || this.config.priority === 'urgent' ? 'critical' : 'normal',
        actions: this.config.actions
          ? options.actions?.map(action => ({
              type: 'button',
              text: action.label,
            }))
          : undefined,
        timeoutType: 'default',
      }) as NotificationWithHandlers;

      if (options.tag) {
        notification.onClick = options.onClick;
        notification.onClose = options.onClose;
        notification.tag = options.tag;
        this.activeNotifications.set(options.tag, notification);
      }

      if (this.config.history) {
        this.addToHistory(options);
      }

      notification.show();

      if (options.timeout) {
        setTimeout(() => {
          notification.close();
        }, options.timeout);
      }
    } catch (error) {
      trackError(error as Error, {
        context: { component: 'NotificationManager', method: 'show' },
      });
    }
  }

  private addToHistory(options: NotificationOptions): void {
    this.history.push({
      ...options,
      timestamp: Date.now(),
    });

    if (this.history.length > this.config.historyLimit) {
      this.history.shift();
    }
  }

  public getHistory(): Array<NotificationOptions & { timestamp: number }> {
    return [...this.history];
  }

  public clearHistory(): void {
    this.history = [];
  }

  public closeAll(): void {
    this.activeNotifications.forEach(notification => {
      notification.close();
    });
    this.activeNotifications.clear();
  }

  public updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  public getConfig(): NotificationConfig {
    return { ...this.config };
  }

  public isEnabled(): boolean {
    return this.config.enabled;
  }

  public enable(): void {
    this.config.enabled = true;
  }

  public disable(): void {
    this.config.enabled = false;
    this.closeAll();
  }
}

export const notificationManager = NotificationManager.getInstance();
