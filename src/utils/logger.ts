import { WriteStream, createWriteStream } from 'fs';
import { join } from 'path';

import { app } from 'electron';

class Logger {
  private logStream: WriteStream;
  private readonly logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3,
  };
  private currentLevel: number;

  constructor() {
    this.currentLevel = this.logLevels.info;
    const logPath = join(app.getPath('userData'), 'logs', 'app.log');
    this.logStream = createWriteStream(logPath, { flags: 'a' });
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const formattedData = data ? `\n${JSON.stringify(data, null, 2)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${formattedData}\n`;
  }

  private log(level: string, message: string, data?: any): void {
    if (this.logLevels[level as keyof typeof this.logLevels] <= this.currentLevel) {
      const formattedMessage = this.formatMessage(level, message, data);
      this.logStream.write(formattedMessage);
      console.log(formattedMessage);
    }
  }

  public setLevel(level: keyof typeof this.logLevels): void {
    this.currentLevel = this.logLevels[level];
  }

  public error(message: string, data?: any): void {
    this.log('error', message, data);
  }

  public warn(message: string, data?: any): void {
    this.log('warn', message, data);
  }

  public info(message: string, data?: any): void {
    this.log('info', message, data);
  }

  public debug(message: string, data?: any): void {
    this.log('debug', message, data);
  }

  public close(): void {
    this.logStream.end();
  }
}

export const logger = new Logger();

// Add custom log levels
logger.levels = {
  ...logger.levels,
  fatal: 60,
  error: 50,
  warn: 40,
  info: 30,
  debug: 20,
  trace: 10,
};

// Add custom log methods
logger.fatal = logger.error.bind(logger);
logger.error = logger.error.bind(logger);
logger.warn = logger.warn.bind(logger);
logger.info = logger.info.bind(logger);
logger.debug = logger.debug.bind(logger);
logger.trace = logger.trace.bind(logger);

// Add request logging middleware
export const requestLogger = (req: any, res: any, next: any) => {
  const start = Date.now();
  res.on('finish', () => {
    const duration = Date.now() - start;
    logger.info({
      method: req.method,
      url: req.url,
      status: res.statusCode,
      duration: `${duration}ms`,
      userAgent: req.get('user-agent'),
      ip: req.ip,
    });
  });
  next();
};

// Add error logging middleware
export const errorLogger = (err: Error, req: any, res: any, next: any) => {
  logger.error({
    error: {
      message: err.message,
      stack: err.stack,
    },
    request: {
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
    },
  });
  next(err);
};
