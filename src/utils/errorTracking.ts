import { join } from 'path';

import { app } from 'electron';

import { logger } from './logger';

interface ErrorContext {
  component?: string;
  method?: string;
  [key: string]: any;
}

export const trackError = (error: Error, context: ErrorContext = {}): void => {
  try {
    const errorLog = {
      timestamp: new Date().toISOString(),
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context,
      appVersion: app.getVersion(),
      platform: process.platform,
    };

    logger.error('Application error:', errorLog);

    // Save error to file
    const errorLogPath = join(app.getPath('userData'), 'logs', 'errors.log');
    // TODO: Implement file logging
  } catch (err) {
    logger.error('Failed to track error:', err);
  }
};
