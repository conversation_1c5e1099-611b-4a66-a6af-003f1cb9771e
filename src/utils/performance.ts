import { BrowserWindow, app } from 'electron';

import { logger } from './logger';

interface PerformanceMetrics {
  cpu: {
    user: number;
    system: number;
    idle: number;
  };
  memory: {
    total: number;
    used: number;
    free: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  renderer: {
    fps: number;
    frameTime: number;
    jsHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

class PerformanceMonitor {
  private metrics: PerformanceMetrics = {
    cpu: { user: 0, system: 0, idle: 0 },
    memory: { total: 0, used: 0, free: 0 },
    network: { bytesIn: 0, bytesOut: 0 },
    renderer: { fps: 0, frameTime: 0, jsHeapSize: 0, jsHeapSizeLimit: 0 },
  };

  private interval: NodeJS.Timeout | null = null;
  private readonly updateInterval = 5000; // 5 seconds

  constructor() {
    this.startMonitoring();
  }

  private startMonitoring() {
    this.interval = setInterval(() => {
      this.updateMetrics();
    }, this.updateInterval);

    // Monitor window performance
    app.on('browser-window-created', (_, window) => {
      this.monitorWindowPerformance(window);
    });
  }

  private updateMetrics() {
    // Update CPU metrics
    const cpuUsage = process.cpuUsage();
    this.metrics.cpu = {
      user: cpuUsage.user,
      system: cpuUsage.system,
      idle: 100 - (cpuUsage.user + cpuUsage.system),
    };

    // Update memory metrics
    const memoryUsage = process.memoryUsage();
    this.metrics.memory = {
      total: memoryUsage.heapTotal,
      used: memoryUsage.heapUsed,
      free: memoryUsage.heapTotal - memoryUsage.heapUsed,
    };

    // Log metrics
    logger.debug('Performance metrics:', this.metrics);

    // Check for performance issues
    this.checkPerformanceIssues();
  }

  private monitorWindowPerformance(window: BrowserWindow) {
    // Monitor FPS
    let frameCount = 0;
    let lastTime = Date.now();

    window.webContents.on('paint', () => {
      frameCount++;
      const currentTime = Date.now();
      const elapsed = currentTime - lastTime;

      if (elapsed >= 1000) {
        const fps = Math.round((frameCount * 1000) / elapsed);
        this.metrics.renderer.fps = fps;
        frameCount = 0;
        lastTime = currentTime;
      }
    });

    // Monitor JavaScript heap
    window.webContents.on('did-finish-load', () => {
      window.webContents.executeJavaScript(`
        setInterval(() => {
          const performance = window.performance;
          const memory = performance.memory;
          if (memory) {
            window.electron.send('performance-metrics', {
              jsHeapSize: memory.usedJSHeapSize,
              jsHeapSizeLimit: memory.jsHeapSizeLimit,
            });
          }
        }, 1000);
      `);
    });

    // Listen for renderer metrics
    window.webContents.on('ipc-message', (_, channel, data) => {
      if (channel === 'performance-metrics') {
        this.metrics.renderer = {
          ...this.metrics.renderer,
          ...data,
        };
      }
    });
  }

  private checkPerformanceIssues() {
    const issues: string[] = [];

    // Check CPU usage
    if (this.metrics.cpu.user + this.metrics.cpu.system > 80) {
      issues.push('High CPU usage detected');
    }

    // Check memory usage
    const memoryUsagePercent = (this.metrics.memory.used / this.metrics.memory.total) * 100;
    if (memoryUsagePercent > 80) {
      issues.push('High memory usage detected');
    }

    // Check FPS
    if (this.metrics.renderer.fps < 30) {
      issues.push('Low FPS detected');
    }

    // Check JavaScript heap
    const heapUsagePercent =
      (this.metrics.renderer.jsHeapSize / this.metrics.renderer.jsHeapSizeLimit) * 100;
    if (heapUsagePercent > 80) {
      issues.push('High JavaScript heap usage detected');
    }

    // Log issues
    if (issues.length > 0) {
      logger.warn('Performance issues detected:', issues);
    }
  }

  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics };
  }

  public stopMonitoring() {
    if (this.interval) {
      clearInterval(this.interval);
      this.interval = null;
    }
  }
}

export const performanceMonitor = new PerformanceMonitor();
