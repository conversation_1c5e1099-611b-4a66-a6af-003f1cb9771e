import { BrowserWindow, app, session } from 'electron';

import { trackError } from '../errorTracking';
import { logger } from '../logger';

import { PerformanceMonitor } from './PerformanceMonitor';

interface OptimizationConfig {
  memory: {
    maxHeapSize: number;
    gcThreshold: number;
  };
  network: {
    maxConcurrentRequests: number;
    requestTimeout: number;
    cacheSize: number;
  };
  renderer: {
    maxTabs: number;
    backgroundThrottling: boolean;
    hardwareAcceleration: boolean;
  };
  disk: {
    maxCacheSize: number;
    cleanupInterval: number;
  };
}

export class PerformanceOptimizer {
  private static instance: PerformanceOptimizer;
  private monitor: PerformanceMonitor;
  private config: OptimizationConfig;
  private window: BrowserWindow | null = null;

  private constructor() {
    this.monitor = PerformanceMonitor.getInstance();
    this.config = {
      memory: {
        maxHeapSize: 1024 * 1024 * 1024, // 1GB
        gcThreshold: 0.8, // 80%
      },
      network: {
        maxConcurrentRequests: 6,
        requestTimeout: 30000, // 30 seconds
        cacheSize: 100 * 1024 * 1024, // 100MB
      },
      renderer: {
        maxTabs: 50,
        backgroundThrottling: true,
        hardwareAcceleration: true,
      },
      disk: {
        maxCacheSize: 500 * 1024 * 1024, // 500MB
        cleanupInterval: 3600000, // 1 hour
      },
    };
  }

  public static getInstance(): PerformanceOptimizer {
    if (!PerformanceOptimizer.instance) {
      PerformanceOptimizer.instance = new PerformanceOptimizer();
    }
    return PerformanceOptimizer.instance;
  }

  public setWindow(window: BrowserWindow): void {
    this.window = window;
    this.monitor.setWindow(window);
  }

  public async initialize(): Promise<void> {
    try {
      // Enable hardware acceleration
      if (this.config.renderer.hardwareAcceleration) {
        app.disableHardwareAcceleration();
      }

      // Configure session
      const ses = session.defaultSession;
      await this.configureSession(ses);

      // Start performance monitoring
      this.monitor.startMonitoring();

      // Set up periodic cleanup
      setInterval(() => this.cleanup(), this.config.disk.cleanupInterval);

      logger.info('Performance optimizer initialized');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.initialize' });
    }
  }

  private async configureSession(ses: Electron.Session): Promise<void> {
    try {
      // Configure cache
      await ses.setCacheSize(this.config.network.cacheSize);

      // Configure permissions
      await ses.setPermissionRequestHandler((webContents, permission, callback) => {
        const url = webContents.getURL();
        // Implement permission logic here
        callback(true);
      });

      // Configure web preferences
      if (this.window) {
        this.window.webContents.setBackgroundThrottling(this.config.renderer.backgroundThrottling);
      }

      logger.info('Session configured for performance optimization');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.configureSession' });
    }
  }

  public async optimize(): Promise<void> {
    try {
      const metrics = this.monitor.getMetrics();

      // Memory optimization
      if (
        metrics.memory.heapUsed >
        this.config.memory.maxHeapSize * this.config.memory.gcThreshold
      ) {
        await this.optimizeMemory();
      }

      // Network optimization
      if (metrics.network.latency > 1000) {
        // 1 second
        await this.optimizeNetwork();
      }

      // Renderer optimization
      if (metrics.renderer.fps < 30) {
        await this.optimizeRenderer();
      }

      logger.info('Performance optimization completed');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.optimize' });
    }
  }

  private async optimizeMemory(): Promise<void> {
    try {
      // Clear memory caches
      if (this.window) {
        await this.window.webContents.session.clearCache();
        await this.window.webContents.session.clearStorageData();
      }

      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }

      logger.info('Memory optimization completed');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.optimizeMemory' });
    }
  }

  private async optimizeNetwork(): Promise<void> {
    try {
      if (this.window) {
        // Clear network caches
        await this.window.webContents.session.clearCache();

        // Reset network settings
        await this.window.webContents.session.setProxy({
          mode: 'system',
        });
      }

      logger.info('Network optimization completed');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.optimizeNetwork' });
    }
  }

  private async optimizeRenderer(): Promise<void> {
    try {
      if (this.window) {
        // Reload the page
        await this.window.webContents.reload();

        // Reset renderer process
        this.window.webContents.setBackgroundThrottling(false);
        setTimeout(() => {
          this.window?.webContents.setBackgroundThrottling(
            this.config.renderer.backgroundThrottling
          );
        }, 1000);
      }

      logger.info('Renderer optimization completed');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.optimizeRenderer' });
    }
  }

  private async cleanup(): Promise<void> {
    try {
      // Clear old cache data
      if (this.window) {
        const ses = this.window.webContents.session;
        const cacheSize = await ses.getCacheSize();

        if (cacheSize > this.config.disk.maxCacheSize) {
          await ses.clearCache();
        }
      }

      logger.info('Cleanup completed');
    } catch (error) {
      trackError(error as Error, { context: 'PerformanceOptimizer.cleanup' });
    }
  }

  public setConfig(config: Partial<OptimizationConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };
    logger.info('Performance optimization config updated', { config: this.config });
  }

  public getConfig(): OptimizationConfig {
    return { ...this.config };
  }
}
