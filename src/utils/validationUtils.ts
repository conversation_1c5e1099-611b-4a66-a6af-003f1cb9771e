import { AbortController } from 'node-abort-controller';

export const asyncWithRetry = async <T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 1000
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    if (retries <= 0) throw error;
    await new Promise(r => setTimeout(r, delay * (4 - retries)));
    return asyncWithRetry(fn, retries - 1, delay);
  }
};

export const createAbortableValidator = (validator: (signal?: AbortSignal) => Promise<boolean>) => {
  const controller = new AbortController();

  return {
    validate: () => validator(controller.signal),
    abort: () => controller.abort(),
  };
};

export const securitySanitizer = (input: string): string => {
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;');
};

// Типизированные схемы валидации
export type ValidationSchema<T> = {
  [K in keyof T]: (value: T[K]) => string | boolean | Promise<string | boolean>;
};

// Универсальный интерфейс ошибок
export interface ValidationErrors<T> {
  [key: string]: string | undefined;
  form?: string;
}
