/**
 * Современный Button компонент с поддержкой вариантов, размеров и состояний
 */

import { type VariantProps, cva } from 'class-variance-authority';
import { Loader2 } from 'lucide-react';
import React, { ButtonHTMLAttributes, forwardRef } from 'react';

import { cn } from '@/utils/cn';

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'text-primary underline-offset-4 hover:underline',
        gradient:
          'bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70',
        glass:
          'bg-white/10 backdrop-blur-sm border border-white/20 text-foreground hover:bg-white/20',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
        xl: 'h-12 rounded-lg px-10 text-base',
        icon: 'h-10 w-10',
        'icon-sm': 'h-8 w-8',
        'icon-lg': 'h-12 w-12',
      },
      animation: {
        none: '',
        pulse: 'animate-pulse',
        bounce: 'hover:animate-bounce',
        scale: 'hover:scale-105 transition-transform',
        glow: 'hover:shadow-lg hover:shadow-primary/25',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      animation: 'none',
    },
  }
);

export interface ButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  fullWidth?: boolean;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      className,
      variant,
      size,
      animation,
      asChild = false,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      fullWidth = false,
      disabled,
      children,
      ...props
    },
    ref
  ) => {
    const isDisabled = disabled || loading;

    const buttonContent = (
      <>
        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {!loading && leftIcon && <span className="mr-2">{leftIcon}</span>}
        {loading && loadingText ? loadingText : children}
        {!loading && rightIcon && <span className="ml-2">{rightIcon}</span>}
      </>
    );

    if (asChild) {
      return (
        <span
          className={cn(
            buttonVariants({ variant, size, animation, className }),
            fullWidth && 'w-full'
          )}
        >
          {buttonContent}
        </span>
      );
    }

    return (
      <button
        className={cn(
          buttonVariants({ variant, size, animation, className }),
          fullWidth && 'w-full'
        )}
        ref={ref}
        disabled={isDisabled}
        {...props}
      >
        {buttonContent}
      </button>
    );
  }
);

Button.displayName = 'Button';

// Button Group Component
export interface ButtonGroupProps {
  children: React.ReactNode;
  className?: string;
  orientation?: 'horizontal' | 'vertical';
  size?: VariantProps<typeof buttonVariants>['size'];
  variant?: VariantProps<typeof buttonVariants>['variant'];
}

export const ButtonGroup = forwardRef<HTMLDivElement, ButtonGroupProps>(
  ({ children, className, orientation = 'horizontal', size, variant, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn(
          'inline-flex',
          orientation === 'horizontal' ? 'flex-row' : 'flex-col',
          '[&>button]:rounded-none',
          '[&>button:first-child]:rounded-l-md',
          '[&>button:last-child]:rounded-r-md',
          orientation === 'vertical' && [
            '[&>button:first-child]:rounded-t-md [&>button:first-child]:rounded-l-none',
            '[&>button:last-child]:rounded-b-md [&>button:last-child]:rounded-r-none',
          ],
          '[&>button:not(:first-child)]:border-l-0',
          orientation === 'vertical' &&
            '[&>button:not(:first-child)]:border-l [&>button:not(:first-child)]:border-t-0',
          className
        )}
        {...props}
      >
        {React.Children.map(children, child => {
          if (React.isValidElement(child) && child.type === Button) {
            return React.cloneElement(child, {
              size: child.props.size || size,
              variant: child.props.variant || variant,
            } as any);
          }
          return child;
        })}
      </div>
    );
  }
);

ButtonGroup.displayName = 'ButtonGroup';

// Icon Button Component
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  icon: React.ReactNode;
  'aria-label': string;
}

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  ({ icon, className, size = 'icon', ...props }, ref) => {
    return (
      <Button ref={ref} size={size} className={className} {...props}>
        {icon}
      </Button>
    );
  }
);

IconButton.displayName = 'IconButton';

// Floating Action Button Component
export interface FABProps extends ButtonProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
  extended?: boolean;
}

export const FloatingActionButton = forwardRef<HTMLButtonElement, FABProps>(
  (
    {
      className,
      position = 'bottom-right',
      extended = false,
      size = extended ? 'default' : 'icon',
      variant = 'default',
      animation = 'scale',
      ...props
    },
    ref
  ) => {
    const positionClasses = {
      'bottom-right': 'fixed bottom-4 right-4',
      'bottom-left': 'fixed bottom-4 left-4',
      'top-right': 'fixed top-4 right-4',
      'top-left': 'fixed top-4 left-4',
    };

    return (
      <Button
        ref={ref}
        size={size}
        variant={variant}
        animation={animation}
        className={cn(
          positionClasses[position],
          'z-50 shadow-lg',
          extended ? 'rounded-full px-6' : 'rounded-full',
          className
        )}
        {...props}
      />
    );
  }
);

FloatingActionButton.displayName = 'FloatingActionButton';

// Split Button Component
export interface SplitButtonProps {
  children: React.ReactNode;
  onMainClick?: () => void;
  onMenuClick?: () => void;
  variant?: VariantProps<typeof buttonVariants>['variant'];
  size?: VariantProps<typeof buttonVariants>['size'];
  disabled?: boolean;
  loading?: boolean;
  className?: string;
}

export const SplitButton = forwardRef<HTMLDivElement, SplitButtonProps>(
  (
    {
      children,
      onMainClick,
      onMenuClick,
      variant = 'default',
      size = 'default',
      disabled = false,
      loading = false,
      className,
      ...props
    },
    ref
  ) => {
    return (
      <div ref={ref} className={cn('inline-flex', className)} {...props}>
        <Button
          variant={variant}
          size={size}
          disabled={disabled}
          loading={loading}
          onClick={onMainClick}
          className="rounded-r-none border-r-0"
        >
          {children}
        </Button>
        <Button
          variant={variant}
          size={size}
          disabled={disabled}
          onClick={onMenuClick}
          className="rounded-l-none px-2"
        >
          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </Button>
      </div>
    );
  }
);

SplitButton.displayName = 'SplitButton';

// Toggle Button Component
export interface ToggleButtonProps extends Omit<ButtonProps, 'variant'> {
  pressed?: boolean;
  onPressedChange?: (pressed: boolean) => void;
  pressedVariant?: VariantProps<typeof buttonVariants>['variant'];
  unpressedVariant?: VariantProps<typeof buttonVariants>['variant'];
}

export const ToggleButton = forwardRef<HTMLButtonElement, ToggleButtonProps>(
  (
    {
      pressed = false,
      onPressedChange,
      pressedVariant = 'default',
      unpressedVariant = 'outline',
      onClick,
      ...props
    },
    ref
  ) => {
    const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
      onPressedChange?.(!pressed);
      onClick?.(event);
    };

    return (
      <Button
        ref={ref}
        variant={pressed ? pressedVariant : unpressedVariant}
        onClick={handleClick}
        aria-pressed={pressed}
        {...props}
      />
    );
  }
);

ToggleButton.displayName = 'ToggleButton';

export { Button, buttonVariants };
export type { ButtonProps };
