import { <PERSON>ert, AlertColor, Snackbar } from '@mui/material';
import React, { ReactNode, createContext, useContext, useState } from 'react';

interface Notification {
  id: string;
  message: string;
  type: AlertColor;
  duration?: number;
}

interface NotificationContextType {
  showNotification: (message: string, type: AlertColor, duration?: number) => void;
}

const NotificationContext = createContext<NotificationContextType>({
  showNotification: () => {},
});

export const useNotification = () => useContext(NotificationContext);

interface NotificationProviderProps {
  children: ReactNode;
}

const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [currentNotification, setCurrentNotification] = useState<Notification | null>(null);

  const showNotification = (message: string, type: AlertColor, duration = 3000) => {
    const id = Math.random().toString(36).substr(2, 9);
    const notification: Notification = { id, message, type, duration };
    setNotifications(prev => [...prev, notification]);

    if (!currentNotification) {
      setCurrentNotification(notification);
    }
  };

  const handleClose = () => {
    setCurrentNotification(null);
    setNotifications(prev => prev.slice(1));
  };

  const handleExited = () => {
    if (notifications.length > 0) {
      setCurrentNotification(notifications[0]);
    }
  };

  return (
    <NotificationContext.Provider value={{ showNotification }}>
      {children}
      <Snackbar
        open={!!currentNotification}
        autoHideDuration={currentNotification?.duration}
        onClose={handleClose}
        onExited={handleExited}
        anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        {currentNotification && (
          <Alert
            onClose={handleClose}
            severity={currentNotification.type}
            variant="filled"
            sx={{ width: '100%' }}
          >
            {currentNotification.message}
          </Alert>
        )}
      </Snackbar>
    </NotificationContext.Provider>
  );
};

export default NotificationProvider;
