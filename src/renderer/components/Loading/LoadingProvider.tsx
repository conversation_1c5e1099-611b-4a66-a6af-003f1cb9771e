import { Backdrop, CircularProgress } from '@mui/material';
import { styled } from '@mui/material/styles';
import React, { ReactNode, createContext, useCallback, useContext, useState } from 'react';

interface LoadingContextType {
  isLoading: boolean;
  loadingCount: number;
  showLoading: () => void;
  hideLoading: () => void;
  startLoading: () => void;
  stopLoading: () => void;
  setLoadingMessage: (message: string) => void;
  loadingMessage: string;
}

const LoadingContext = createContext<LoadingContextType>({
  isLoading: false,
  loadingCount: 0,
  showLoading: () => {},
  hideLoading: () => {},
  startLoading: () => {},
  stopLoading: () => {},
  setLoadingMessage: () => {},
  loadingMessage: '',
});

export const useLoading = () => useContext(LoadingContext);

const StyledBackdrop = styled(Backdrop)(({ theme }) => ({
  color: theme.palette.primary.main,
  zIndex: theme.zIndex.drawer + 1,
  flexDirection: 'column',
  gap: theme.spacing(2),
}));

interface LoadingProviderProps {
  children: ReactNode;
}

const LoadingProvider: React.FC<LoadingProviderProps> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [loadingCount, setLoadingCount] = useState(0);
  const [loadingMessage, setLoadingMessage] = useState('');

  // Enhanced loading management with counter
  const startLoading = useCallback(() => {
    setLoadingCount(prev => {
      const newCount = prev + 1;
      setIsLoading(newCount > 0);
      return newCount;
    });
  }, []);

  const stopLoading = useCallback(() => {
    setLoadingCount(prev => {
      const newCount = Math.max(0, prev - 1);
      setIsLoading(newCount > 0);
      return newCount;
    });
  }, []);

  // Simple loading management (legacy compatibility)
  const showLoading = useCallback(() => {
    setIsLoading(true);
    setLoadingCount(1);
  }, []);

  const hideLoading = useCallback(() => {
    setIsLoading(false);
    setLoadingCount(0);
  }, []);

  const handleSetLoadingMessage = useCallback((message: string) => {
    setLoadingMessage(message);
  }, []);

  return (
    <LoadingContext.Provider value={{
      isLoading,
      loadingCount,
      showLoading,
      hideLoading,
      startLoading,
      stopLoading,
      setLoadingMessage: handleSetLoadingMessage,
      loadingMessage
    }}>
      {children}
      <StyledBackdrop open={isLoading}>
        <CircularProgress color="inherit" />
        {loadingMessage && (
          <div style={{ marginTop: 16, textAlign: 'center' }}>
            {loadingMessage}
          </div>
        )}
      </StyledBackdrop>
    </LoadingContext.Provider>
  );
};

export default LoadingProvider;
