import { EventEmitter } from 'events';

import { configManager } from '../core/ConfigurationManager';
import { logger } from '../core/EnhancedLogger';
import { securityScanner } from '../security/SecurityScanner';

export interface Extension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  permissions: ExtensionPermission[];
  manifest: ExtensionManifest;
  enabled: boolean;
  installed: boolean;
  verified: boolean;
  sandboxed: boolean;
  loadTime: number;
  memoryUsage: number;
  cpuUsage: number;
  networkRequests: number;
  lastUpdated: number;
  installDate: number;
  updateAvailable: boolean;
  rating: number;
  downloadCount: number;
}

export interface ExtensionManifest {
  manifest_version: number;
  name: string;
  version: string;
  description: string;
  permissions: string[];
  background?: {
    scripts: string[];
    persistent: boolean;
  };
  content_scripts?: Array<{
    matches: string[];
    js: string[];
    css?: string[];
    run_at: 'document_start' | 'document_end' | 'document_idle';
  }>;
  browser_action?: {
    default_title: string;
    default_popup?: string;
    default_icon?: string;
  };
  page_action?: {
    default_title: string;
    default_popup?: string;
    default_icon?: string;
  };
  web_accessible_resources?: string[];
  externally_connectable?: {
    matches: string[];
  };
  content_security_policy?: string;
}

export interface ExtensionPermission {
  name: string;
  description: string;
  required: boolean;
  granted: boolean;
  dangerous: boolean;
  category:
    | 'storage'
    | 'network'
    | 'tabs'
    | 'bookmarks'
    | 'history'
    | 'cookies'
    | 'privacy'
    | 'system';
}

export interface ExtensionStore {
  id: string;
  name: string;
  url: string;
  verified: boolean;
  extensions: StoreExtension[];
}

export interface StoreExtension {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  category: string;
  rating: number;
  downloadCount: number;
  price: number;
  screenshots: string[];
  verified: boolean;
  lastUpdated: number;
}

export interface ExtensionSandbox {
  id: string;
  extensionId: string;
  isolated: boolean;
  permissions: string[];
  allowedDomains: string[];
  blockedDomains: string[];
  resourceLimits: {
    memory: number;
    cpu: number;
    network: number;
    storage: number;
  };
}

export class ExtensionManager extends EventEmitter {
  private static instance: ExtensionManager;
  private extensions: Map<string, Extension> = new Map();
  private sandboxes: Map<string, ExtensionSandbox> = new Map();
  private stores: Map<string, ExtensionStore> = new Map();
  private permissionRequests: Map<string, ExtensionPermission[]> = new Map();
  private updateChecker: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.initializeExtensionManager();
  }

  public static getInstance(): ExtensionManager {
    if (!ExtensionManager.instance) {
      ExtensionManager.instance = new ExtensionManager();
    }
    return ExtensionManager.instance;
  }

  private async initializeExtensionManager(): Promise<void> {
    // Загрузка установленных расширений
    await this.loadInstalledExtensions();

    // Настройка магазинов расширений
    await this.setupExtensionStores();

    // Запуск проверки обновлений
    this.startUpdateChecker();

    // Настройка песочниц
    this.setupSandboxes();

    logger.info('Extension manager initialized', {
      extensionCount: this.extensions.size,
      storeCount: this.stores.size,
      sandboxCount: this.sandboxes.size,
    });
  }

  public async installExtension(
    extensionId: string,
    source: 'store' | 'file' | 'url',
    data?: any
  ): Promise<Extension> {
    try {
      // Получение манифеста расширения
      const manifest = await this.getExtensionManifest(extensionId, source, data);

      // Проверка безопасности
      const securityCheck = await this.performSecurityCheck(manifest, data);
      if (!securityCheck.safe) {
        throw new Error(`Security check failed: ${securityCheck.reason}`);
      }

      // Проверка разрешений
      const permissions = this.parsePermissions(manifest.permissions);
      const permissionGranted = await this.requestPermissions(extensionId, permissions);
      if (!permissionGranted) {
        throw new Error('Required permissions not granted');
      }

      // Создание песочницы
      const sandbox = await this.createSandbox(extensionId, permissions);

      // Установка расширения
      const extension: Extension = {
        id: extensionId,
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: 'Unknown', // Будет извлечено из метаданных
        permissions,
        manifest,
        enabled: true,
        installed: true,
        verified: securityCheck.verified,
        sandboxed: true,
        loadTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        networkRequests: 0,
        lastUpdated: Date.now(),
        installDate: Date.now(),
        updateAvailable: false,
        rating: 0,
        downloadCount: 0,
      };

      // Загрузка расширения
      await this.loadExtension(extension, sandbox);

      this.extensions.set(extensionId, extension);
      this.sandboxes.set(sandbox.id, sandbox);

      this.emit('extension_installed', extension);
      logger.info('Extension installed successfully', {
        extensionId,
        name: extension.name,
        version: extension.version,
      });

      return extension;
    } catch (error) {
      logger.error('Extension installation failed', error, { extensionId });
      throw error;
    }
  }

  public async uninstallExtension(extensionId: string): Promise<void> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    try {
      // Остановка расширения
      await this.stopExtension(extensionId);

      // Удаление песочницы
      const sandbox = Array.from(this.sandboxes.values()).find(s => s.extensionId === extensionId);
      if (sandbox) {
        await this.destroySandbox(sandbox.id);
        this.sandboxes.delete(sandbox.id);
      }

      // Очистка данных расширения
      await this.cleanupExtensionData(extensionId);

      this.extensions.delete(extensionId);

      this.emit('extension_uninstalled', { extensionId, extension });
      logger.info('Extension uninstalled successfully', { extensionId, name: extension.name });
    } catch (error) {
      logger.error('Extension uninstallation failed', error, { extensionId });
      throw error;
    }
  }

  public async enableExtension(extensionId: string): Promise<void> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (extension.enabled) {
      return;
    }

    try {
      // Проверка разрешений
      const permissionsValid = await this.validatePermissions(extension);
      if (!permissionsValid) {
        throw new Error('Extension permissions are no longer valid');
      }

      // Загрузка расширения
      const sandbox = Array.from(this.sandboxes.values()).find(s => s.extensionId === extensionId);
      if (sandbox) {
        await this.loadExtension(extension, sandbox);
      }

      extension.enabled = true;
      extension.lastUpdated = Date.now();

      this.emit('extension_enabled', extension);
      logger.info('Extension enabled', { extensionId, name: extension.name });
    } catch (error) {
      logger.error('Extension enabling failed', error, { extensionId });
      throw error;
    }
  }

  public async disableExtension(extensionId: string): Promise<void> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    if (!extension.enabled) {
      return;
    }

    try {
      await this.stopExtension(extensionId);
      extension.enabled = false;
      extension.lastUpdated = Date.now();

      this.emit('extension_disabled', extension);
      logger.info('Extension disabled', { extensionId, name: extension.name });
    } catch (error) {
      logger.error('Extension disabling failed', error, { extensionId });
      throw error;
    }
  }

  public async updateExtension(extensionId: string): Promise<Extension> {
    const extension = this.extensions.get(extensionId);
    if (!extension) {
      throw new Error(`Extension ${extensionId} not found`);
    }

    try {
      // Проверка доступности обновления
      const updateInfo = await this.checkForUpdate(extensionId);
      if (!updateInfo.available) {
        throw new Error('No update available');
      }

      // Загрузка нового манифеста
      const newManifest = await this.getExtensionManifest(extensionId, 'store');

      // Проверка безопасности новой версии
      const securityCheck = await this.performSecurityCheck(newManifest);
      if (!securityCheck.safe) {
        throw new Error(`Security check failed for update: ${securityCheck.reason}`);
      }

      // Остановка текущей версии
      await this.stopExtension(extensionId);

      // Обновление расширения
      extension.version = newManifest.version;
      extension.manifest = newManifest;
      extension.lastUpdated = Date.now();
      extension.updateAvailable = false;

      // Перезагрузка расширения
      const sandbox = Array.from(this.sandboxes.values()).find(s => s.extensionId === extensionId);
      if (sandbox && extension.enabled) {
        await this.loadExtension(extension, sandbox);
      }

      this.emit('extension_updated', extension);
      logger.info('Extension updated successfully', {
        extensionId,
        name: extension.name,
        version: extension.version,
      });

      return extension;
    } catch (error) {
      logger.error('Extension update failed', error, { extensionId });
      throw error;
    }
  }

  private async getExtensionManifest(
    extensionId: string,
    source: string,
    data?: any
  ): Promise<ExtensionManifest> {
    // В реальной реализации здесь будет загрузка манифеста из различных источников
    return {
      manifest_version: 3,
      name: 'Sample Extension',
      version: '1.0.0',
      description: 'A sample extension for demonstration',
      permissions: ['tabs', 'storage'],
      background: {
        scripts: ['background.js'],
        persistent: false,
      },
      content_scripts: [
        {
          matches: ['<all_urls>'],
          js: ['content.js'],
          run_at: 'document_end',
        },
      ],
    };
  }

  private async performSecurityCheck(
    manifest: ExtensionManifest,
    data?: any
  ): Promise<{
    safe: boolean;
    verified: boolean;
    reason?: string;
  }> {
    try {
      // Проверка манифеста на подозрительные разрешения
      const dangerousPermissions = ['nativeMessaging', 'debugger', 'management'];
      const hasDangerousPermissions = manifest.permissions.some(p =>
        dangerousPermissions.includes(p)
      );

      // Проверка CSP
      const hasWeakCSP =
        manifest.content_security_policy?.includes('unsafe-eval') ||
        manifest.content_security_policy?.includes('unsafe-inline');

      // Сканирование на вредоносный код
      const malwareCheck = await securityScanner.scanExtension(manifest, data);

      if (malwareCheck.threats.length > 0) {
        return {
          safe: false,
          verified: false,
          reason: 'Malware detected in extension',
        };
      }

      if (hasDangerousPermissions && !this.isVerifiedDeveloper(manifest)) {
        return {
          safe: false,
          verified: false,
          reason: 'Dangerous permissions from unverified developer',
        };
      }

      if (hasWeakCSP) {
        return {
          safe: false,
          verified: false,
          reason: 'Weak Content Security Policy',
        };
      }

      return {
        safe: true,
        verified: this.isVerifiedDeveloper(manifest),
      };
    } catch (error) {
      logger.error('Security check failed', error);
      return {
        safe: false,
        verified: false,
        reason: 'Security check error',
      };
    }
  }

  private parsePermissions(permissions: string[]): ExtensionPermission[] {
    const permissionMap: Record<
      string,
      { description: string; dangerous: boolean; category: ExtensionPermission['category'] }
    > = {
      tabs: { description: 'Access browser tabs', dangerous: false, category: 'tabs' },
      storage: { description: 'Store data locally', dangerous: false, category: 'storage' },
      bookmarks: {
        description: 'Read and modify bookmarks',
        dangerous: false,
        category: 'bookmarks',
      },
      history: { description: 'Read browsing history', dangerous: true, category: 'history' },
      cookies: { description: 'Read and modify cookies', dangerous: true, category: 'cookies' },
      nativeMessaging: {
        description: 'Communicate with native applications',
        dangerous: true,
        category: 'system',
      },
      debugger: { description: 'Access debugging APIs', dangerous: true, category: 'system' },
      management: { description: 'Manage other extensions', dangerous: true, category: 'system' },
    };

    return permissions.map(permission => ({
      name: permission,
      description: permissionMap[permission]?.description || 'Unknown permission',
      required: true,
      granted: false,
      dangerous: permissionMap[permission]?.dangerous || false,
      category: permissionMap[permission]?.category || 'system',
    }));
  }

  private async requestPermissions(
    extensionId: string,
    permissions: ExtensionPermission[]
  ): Promise<boolean> {
    // В реальной реализации здесь будет показан диалог пользователю
    const dangerousPermissions = permissions.filter(p => p.dangerous);

    if (dangerousPermissions.length > 0) {
      // Имитация запроса разрешений у пользователя
      logger.warn('Extension requests dangerous permissions', {
        extensionId,
        dangerousPermissions: dangerousPermissions.map(p => p.name),
      });
    }

    // Автоматическое предоставление разрешений для демонстрации
    permissions.forEach(permission => {
      permission.granted = true;
    });

    return true;
  }

  private async createSandbox(
    extensionId: string,
    permissions: ExtensionPermission[]
  ): Promise<ExtensionSandbox> {
    const sandboxId = `sandbox_${extensionId}_${Date.now()}`;

    const sandbox: ExtensionSandbox = {
      id: sandboxId,
      extensionId,
      isolated: true,
      permissions: permissions.filter(p => p.granted).map(p => p.name),
      allowedDomains: ['*'], // Будет настроено на основе манифеста
      blockedDomains: [],
      resourceLimits: {
        memory: 100 * 1024 * 1024, // 100MB
        cpu: 10, // 10% CPU
        network: 1024 * 1024, // 1MB/s
        storage: 10 * 1024 * 1024, // 10MB
      },
    };

    return sandbox;
  }

  private async loadExtension(extension: Extension, sandbox: ExtensionSandbox): Promise<void> {
    const startTime = performance.now();

    try {
      // Загрузка фоновых скриптов
      if (extension.manifest.background?.scripts) {
        await this.loadBackgroundScripts(extension, sandbox);
      }

      // Регистрация контент-скриптов
      if (extension.manifest.content_scripts) {
        await this.registerContentScripts(extension, sandbox);
      }

      // Настройка действий браузера
      if (extension.manifest.browser_action) {
        await this.setupBrowserAction(extension);
      }

      extension.loadTime = performance.now() - startTime;

      this.emit('extension_loaded', extension);
      logger.debug('Extension loaded successfully', {
        extensionId: extension.id,
        loadTime: extension.loadTime,
      });
    } catch (error) {
      logger.error('Extension loading failed', error, { extensionId: extension.id });
      throw error;
    }
  }

  private async loadBackgroundScripts(
    extension: Extension,
    sandbox: ExtensionSandbox
  ): Promise<void> {
    // В реальной реализации здесь будет загрузка и выполнение фоновых скриптов в песочнице
    logger.debug('Loading background scripts', { extensionId: extension.id });
  }

  private async registerContentScripts(
    extension: Extension,
    sandbox: ExtensionSandbox
  ): Promise<void> {
    // В реальной реализации здесь будет регистрация контент-скриптов
    logger.debug('Registering content scripts', { extensionId: extension.id });
  }

  private async setupBrowserAction(extension: Extension): Promise<void> {
    // В реальной реализации здесь будет настройка действий в панели инструментов
    logger.debug('Setting up browser action', { extensionId: extension.id });
  }

  private async stopExtension(extensionId: string): Promise<void> {
    // Остановка всех процессов расширения
    logger.debug('Stopping extension', { extensionId });
  }

  private async destroySandbox(sandboxId: string): Promise<void> {
    // Уничтожение песочницы
    logger.debug('Destroying sandbox', { sandboxId });
  }

  private async cleanupExtensionData(extensionId: string): Promise<void> {
    // Очистка данных расширения
    logger.debug('Cleaning up extension data', { extensionId });
  }

  private async validatePermissions(extension: Extension): Promise<boolean> {
    // Проверка актуальности разрешений
    return true;
  }

  private async checkForUpdate(
    extensionId: string
  ): Promise<{ available: boolean; version?: string }> {
    // Проверка доступности обновлений
    return { available: false };
  }

  private isVerifiedDeveloper(manifest: ExtensionManifest): boolean {
    // Проверка верификации разработчика
    return false;
  }

  private async loadInstalledExtensions(): Promise<void> {
    // Загрузка установленных расширений из хранилища
    logger.debug('Loading installed extensions');
  }

  private async setupExtensionStores(): Promise<void> {
    // Настройка магазинов расширений
    const defaultStore: ExtensionStore = {
      id: 'official',
      name: 'A14 Browser Extensions',
      url: 'https://extensions.a14browser.com',
      verified: true,
      extensions: [],
    };

    this.stores.set(defaultStore.id, defaultStore);
  }

  private startUpdateChecker(): void {
    this.updateChecker = setInterval(
      async () => {
        await this.checkAllExtensionsForUpdates();
      },
      24 * 60 * 60 * 1000
    ); // Проверка раз в день
  }

  private async checkAllExtensionsForUpdates(): Promise<void> {
    for (const extension of this.extensions.values()) {
      try {
        const updateInfo = await this.checkForUpdate(extension.id);
        extension.updateAvailable = updateInfo.available;

        if (updateInfo.available) {
          this.emit('extension_update_available', { extension, newVersion: updateInfo.version });
        }
      } catch (error) {
        logger.warn('Failed to check for extension update', error, { extensionId: extension.id });
      }
    }
  }

  private setupSandboxes(): void {
    // Настройка системы песочниц
    logger.debug('Setting up extension sandboxes');
  }

  // Геттеры
  public getExtensions(): Extension[] {
    return Array.from(this.extensions.values());
  }

  public getExtension(extensionId: string): Extension | null {
    return this.extensions.get(extensionId) || null;
  }

  public getEnabledExtensions(): Extension[] {
    return Array.from(this.extensions.values()).filter(ext => ext.enabled);
  }

  public getExtensionStores(): ExtensionStore[] {
    return Array.from(this.stores.values());
  }

  public destroy(): void {
    if (this.updateChecker) {
      clearInterval(this.updateChecker);
    }
    this.removeAllListeners();
  }
}

// Экспорт синглтона
export const extensionManager = ExtensionManager.getInstance();
