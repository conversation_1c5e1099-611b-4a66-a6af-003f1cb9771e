import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import Backend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

import { LANGUAGES } from '../constants';

// Import translations
import enTranslation from './locales/en.json';

const resources = {
  en: {
    translation: enTranslation,
  },
};

const languageConfigs = {
  [LANGUAGES.EN]: {
    name: 'English',
    nativeName: 'English',
    rtl: false,
    fallback: LANGUAGES.EN,
  },
};

i18n
  .use(Backend)
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: LANGUAGES.EN,
    debug: process.env.NODE_ENV === 'development',
    interpolation: {
      escapeValue: false,
    },
    detection: {
      order: ['localStorage', 'navigator'],
      caches: ['localStorage'],
    },
    backend: {
      loadPath: '/locales/{{lng}}/{{ns}}.json',
    },
    react: {
      useSuspense: true,
    },
  });

export { i18n, languageConfigs };

// Language utilities
export const languages = {
  en: {
    name: 'English',
    nativeName: 'English',
    direction: 'ltr',
  },
} as const;

export type LanguageCode = keyof typeof languages;

export const getLanguageName = (code: LanguageCode): string => {
  return languages[code].name;
};

export const getNativeLanguageName = (code: LanguageCode): string => {
  return languages[code].nativeName;
};

export const getLanguageDirection = (code: LanguageCode): 'ltr' | 'rtl' => {
  return languages[code].direction;
};

export const isRTL = (code: LanguageCode): boolean => {
  return languages[code].direction === 'rtl';
};

export const getCurrentLanguage = (): LanguageCode => {
  return (i18n.language as LanguageCode) || 'en';
};

export const setLanguage = async (code: LanguageCode): Promise<void> => {
  await i18n.changeLanguage(code);
  document.documentElement.lang = code;
  document.documentElement.dir = getLanguageDirection(code);
};

export const formatDate = (date: Date, locale: string) => {
  return new Intl.DateTimeFormat(locale, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(date);
};

export const formatNumber = (number: number, locale: string) => {
  return new Intl.NumberFormat(locale).format(number);
};

export const formatCurrency = (amount: number, locale: string, currency: string) => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
  }).format(amount);
};

export const formatRelativeTime = (date: Date, locale: string) => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const seconds = Math.floor(diff / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);

  const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' });

  if (days > 0) return rtf.format(-days, 'day');
  if (hours > 0) return rtf.format(-hours, 'hour');
  if (minutes > 0) return rtf.format(-minutes, 'minute');
  return rtf.format(-seconds, 'second');
};
