import { EventEmitter } from 'events';

import { CacheManager } from '../cache/CacheManager';
import { ErrorManager } from '../error/ErrorManager';
import { PerformanceMonitor } from '../performance/PerformanceMonitor';

interface NetworkConfig {
  baseUrl: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  cacheEnabled: boolean;
  offlineMode: boolean;
  requestQueue: boolean;
  compression: boolean;
  headers: Record<string, string>;
}

interface NetworkRequest {
  id: string;
  method: string;
  url: string;
  headers: Record<string, string>;
  data?: any;
  params?: Record<string, string>;
  cache?: boolean;
  retry?: number;
  timeout?: number;
  priority?: number;
}

interface NetworkResponse {
  id: string;
  status: number;
  headers: Record<string, string>;
  data: any;
  timestamp: number;
  duration: number;
}

interface NetworkError {
  id: string;
  code: string;
  message: string;
  request: NetworkRequest;
  response?: NetworkResponse;
  timestamp: number;
}

export class NetworkManager {
  private static instance: NetworkManager;
  private config: NetworkConfig;
  private eventEmitter: EventEmitter;
  private requestQueue: NetworkRequest[];
  private activeRequests: Map<string, NetworkRequest>;
  private cacheManager: CacheManager;
  private errorManager: ErrorManager;
  private performanceMonitor: PerformanceMonitor;

  private constructor() {
    this.config = {
      baseUrl: '',
      timeout: 30000,
      retryAttempts: 3,
      retryDelay: 1000,
      cacheEnabled: true,
      offlineMode: false,
      requestQueue: true,
      compression: true,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
    };

    this.eventEmitter = new EventEmitter();
    this.requestQueue = [];
    this.activeRequests = new Map();
    this.cacheManager = CacheManager.getInstance();
    this.errorManager = ErrorManager.getInstance();
    this.performanceMonitor = PerformanceMonitor.getInstance();

    // Initialize network monitoring
    this.initializeNetworkMonitoring();
  }

  public static getInstance(): NetworkManager {
    if (!NetworkManager.instance) {
      NetworkManager.instance = new NetworkManager();
    }
    return NetworkManager.instance;
  }

  private initializeNetworkMonitoring(): void {
    // Monitor online/offline status
    window.addEventListener('online', () => this.handleOnlineStatus(true));
    window.addEventListener('offline', () => this.handleOnlineStatus(false));

    // Monitor network quality
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      connection.addEventListener('change', () => this.handleConnectionChange(connection));
    }
  }

  private handleOnlineStatus(online: boolean): void {
    this.config.offlineMode = !online;
    this.eventEmitter.emit('networkStatus', { online });

    if (online) {
      this.processRequestQueue();
    }
  }

  private handleConnectionChange(connection: any): void {
    const networkInfo = {
      effectiveType: connection.effectiveType,
      downlink: connection.downlink,
      rtt: connection.rtt,
      saveData: connection.saveData,
    };

    this.eventEmitter.emit('networkQuality', networkInfo);
  }

  public async request(request: NetworkRequest): Promise<NetworkResponse> {
    const startTime = performance.now();

    try {
      // Check cache first
      if (this.config.cacheEnabled && request.cache) {
        const cachedResponse = await this.getCachedResponse(request);
        if (cachedResponse) {
          return cachedResponse;
        }
      }

      // Check offline mode
      if (this.config.offlineMode) {
        if (this.config.requestQueue) {
          this.queueRequest(request);
          throw new Error('Request queued due to offline mode');
        }
        throw new Error('Network is offline');
      }

      // Prepare request
      const preparedRequest = this.prepareRequest(request);

      // Execute request
      const response = await this.executeRequest(preparedRequest);

      // Cache response if needed
      if (this.config.cacheEnabled && request.cache) {
        await this.cacheResponse(request, response);
      }

      // Track performance
      const duration = performance.now() - startTime;
      this.performanceMonitor.trackMetric('network_request_duration', duration, {
        method: request.method,
        url: request.url,
        status: response.status,
      });

      return response;
    } catch (error) {
      // Handle errors
      const networkError = this.handleRequestError(error, request);

      // Retry if possible
      if (this.shouldRetry(request, networkError)) {
        return this.retryRequest(request);
      }

      throw networkError;
    }
  }

  private prepareRequest(request: NetworkRequest): NetworkRequest {
    return {
      ...request,
      url: this.buildUrl(request.url, request.params),
      headers: {
        ...this.config.headers,
        ...request.headers,
      },
      timeout: request.timeout || this.config.timeout,
      retry: request.retry || this.config.retryAttempts,
    };
  }

  private buildUrl(url: string, params?: Record<string, string>): string {
    const fullUrl = url.startsWith('http') ? url : `${this.config.baseUrl}${url}`;

    if (!params) {
      return fullUrl;
    }

    const queryString = Object.entries(params)
      .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
      .join('&');

    return `${fullUrl}${fullUrl.includes('?') ? '&' : '?'}${queryString}`;
  }

  private async executeRequest(request: NetworkRequest): Promise<NetworkResponse> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), request.timeout);

    try {
      const response = await fetch(request.url, {
        method: request.method,
        headers: request.headers,
        body: request.data ? JSON.stringify(request.data) : undefined,
        signal: controller.signal,
      });

      const data = await response.json();

      return {
        id: request.id,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries()),
        data,
        timestamp: Date.now(),
        duration: performance.now() - performance.now(),
      };
    } finally {
      clearTimeout(timeoutId);
    }
  }

  private async getCachedResponse(request: NetworkRequest): Promise<NetworkResponse | null> {
    const cacheKey = this.getCacheKey(request);
    return this.cacheManager.get(cacheKey);
  }

  private async cacheResponse(request: NetworkRequest, response: NetworkResponse): Promise<void> {
    const cacheKey = this.getCacheKey(request);
    await this.cacheManager.set(cacheKey, response, {
      ttl: 3600, // 1 hour
      tags: ['network', request.method, request.url],
    });
  }

  private getCacheKey(request: NetworkRequest): string {
    return `network:${request.method}:${request.url}:${JSON.stringify(request.params || {})}`;
  }

  private queueRequest(request: NetworkRequest): void {
    this.requestQueue.push(request);
    this.eventEmitter.emit('requestQueued', request);
  }

  private async processRequestQueue(): Promise<void> {
    while (this.requestQueue.length > 0) {
      const request = this.requestQueue.shift();
      if (request) {
        try {
          await this.request(request);
        } catch (error) {
          this.errorManager.handleError(error, {
            context: 'NetworkManager',
            request,
          });
        }
      }
    }
  }

  private handleRequestError(error: any, request: NetworkRequest): NetworkError {
    const networkError: NetworkError = {
      id: request.id,
      code: error.name || 'NETWORK_ERROR',
      message: error.message || 'Network request failed',
      request,
      timestamp: Date.now(),
    };

    this.errorManager.handleError(networkError, {
      context: 'NetworkManager',
      request,
    });

    this.eventEmitter.emit('requestError', networkError);

    return networkError;
  }

  private shouldRetry(request: NetworkRequest, error: NetworkError): boolean {
    if (!request.retry || request.retry <= 0) {
      return false;
    }

    // Don't retry on certain status codes
    if (error.response && [400, 401, 403, 404].includes(error.response.status)) {
      return false;
    }

    return true;
  }

  private async retryRequest(request: NetworkRequest): Promise<NetworkResponse> {
    request.retry = (request.retry || 0) - 1;

    // Add delay before retry
    await new Promise(resolve => setTimeout(resolve, this.config.retryDelay));

    return this.request(request);
  }

  public setConfig(config: Partial<NetworkConfig>): void {
    this.config = {
      ...this.config,
      ...config,
    };
    this.eventEmitter.emit('configUpdated', this.config);
  }

  public getConfig(): NetworkConfig {
    return { ...this.config };
  }

  public on(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.on(event, listener);
  }

  public off(event: string, listener: (...args: any[]) => void): void {
    this.eventEmitter.off(event, listener);
  }

  public clearQueue(): void {
    this.requestQueue = [];
    this.eventEmitter.emit('queueCleared');
  }

  public getActiveRequests(): NetworkRequest[] {
    return Array.from(this.activeRequests.values());
  }

  public getQueueLength(): number {
    return this.requestQueue.length;
  }

  public isOnline(): boolean {
    return !this.config.offlineMode;
  }

  public cleanup(): void {
    this.clearQueue();
    this.activeRequests.clear();
    this.eventEmitter.removeAllListeners();
  }
}
