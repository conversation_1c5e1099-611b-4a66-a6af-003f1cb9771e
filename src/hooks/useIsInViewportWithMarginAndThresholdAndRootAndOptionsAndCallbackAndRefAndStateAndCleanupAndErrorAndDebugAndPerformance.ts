import { useEffect, useRef, useState } from 'react';

interface UseIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorAndDebugAndPerformanceProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  thresholdPercentage?: number;
  rootMargin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  options?: {
    trackVisibility?: boolean;
    delay?: number;
  };
  onIntersectionChange?: (isIntersecting: boolean, entry: IntersectionObserverEntry) => void;
  ref?: React.RefObject<HTMLElement>;
  initialState?: boolean;
  onCleanup?: () => void;
  onError?: (error: Error) => void;
  debug?: boolean;
  performance?: {
    measure?: boolean;
    mark?: boolean;
    log?: boolean;
  };
}

export function useIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorAndDebugAndPerformance<
  T extends HTMLElement = HTMLElement,
>(
  props: UseIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorAndDebugAndPerformanceProps = {}
): [React.RefObject<T>, boolean] {
  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    thresholdPercentage = 0,
    rootMargin: rootMarginOptions = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    options = {
      trackVisibility: false,
      delay: 0,
    },
    onIntersectionChange,
    ref: externalRef,
    initialState = false,
    onCleanup,
    onError,
    debug = false,
    performance: performanceOptions = {
      measure: false,
      mark: false,
      log: false,
    },
  } = props;

  const [isInViewport, setIsInViewport] = useState(initialState);
  const internalRef = useRef<T>(null);
  const elementRef = externalRef || internalRef;
  const timeoutRef = useRef<NodeJS.Timeout>();
  const isFirstRender = useRef(true);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const performanceRef = useRef<{
    marks: { [key: string]: number };
    measures: { [key: string]: number };
  }>({
    marks: {},
    measures: {},
  });

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const element = elementRef.current;

    if (!element) {
      return;
    }

    try {
      if (performanceOptions.mark) {
        performance.mark('intersection-observer-init-start');
      }

      observerRef.current = new IntersectionObserver(
        ([entry]) => {
          try {
            if (performanceOptions.mark) {
              performance.mark('intersection-observer-callback-start');
            }

            const rect = entry.boundingClientRect;
            const rootRect = root
              ? root.getBoundingClientRect()
              : {
                  top: 0,
                  right: window.innerWidth,
                  bottom: window.innerHeight,
                  left: 0,
                };

            const elementArea = rect.width * rect.height;
            const intersectionArea =
              Math.max(
                0,
                Math.min(rect.right + margin.right, rootRect.right + rootMarginOptions.right) -
                  Math.max(rect.left - margin.left, rootRect.left - rootMarginOptions.left)
              ) *
              Math.max(
                0,
                Math.min(rect.bottom + margin.bottom, rootRect.bottom + rootMarginOptions.bottom) -
                  Math.max(rect.top - margin.top, rootRect.top - rootMarginOptions.top)
              );

            const intersectionPercentage = (intersectionArea / elementArea) * 100;
            const isIntersecting = intersectionPercentage >= thresholdPercentage;

            if (debug) {
              console.log('Intersection Observer Entry:', {
                rect,
                rootRect,
                elementArea,
                intersectionArea,
                intersectionPercentage,
                isIntersecting,
                threshold,
                thresholdPercentage,
                margin,
                rootMarginOptions,
                options,
              });
            }

            if (options.delay) {
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }

              timeoutRef.current = setTimeout(() => {
                setIsInViewport(isIntersecting);
                onIntersectionChange?.(isIntersecting, entry);
              }, options.delay);
            } else {
              setIsInViewport(isIntersecting);
              onIntersectionChange?.(isIntersecting, entry);
            }

            if (performanceOptions.mark) {
              performance.mark('intersection-observer-callback-end');
              performance.measure(
                'intersection-observer-callback',
                'intersection-observer-callback-start',
                'intersection-observer-callback-end'
              );
            }
          } catch (error) {
            if (debug) {
              console.error('Error in intersection observer callback:', error);
            }
            onError?.(error instanceof Error ? error : new Error(String(error)));
          }
        },
        {
          threshold,
          root,
          rootMargin,
          trackVisibility: options.trackVisibility,
        }
      );

      observerRef.current.observe(element);

      if (performanceOptions.mark) {
        performance.mark('intersection-observer-init-end');
        performance.measure(
          'intersection-observer-init',
          'intersection-observer-init-start',
          'intersection-observer-init-end'
        );
      }

      if (debug) {
        console.log('Intersection Observer initialized:', {
          element,
          root,
          threshold,
          rootMargin,
          options,
        });
      }
    } catch (error) {
      if (debug) {
        console.error('Error initializing intersection observer:', error);
      }
      onError?.(error instanceof Error ? error : new Error(String(error)));
    }

    return () => {
      try {
        if (performanceOptions.mark) {
          performance.mark('intersection-observer-cleanup-start');
        }

        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        if (observerRef.current) {
          observerRef.current.unobserve(element);
          observerRef.current.disconnect();
        }

        onCleanup?.();

        if (performanceOptions.mark) {
          performance.mark('intersection-observer-cleanup-end');
          performance.measure(
            'intersection-observer-cleanup',
            'intersection-observer-cleanup-start',
            'intersection-observer-cleanup-end'
          );
        }

        if (debug) {
          console.log('Intersection Observer cleaned up');
        }

        if (performanceOptions.log) {
          const measures = performance.getEntriesByType('measure');
          console.log('Performance measures:', measures);
        }
      } catch (error) {
        if (debug) {
          console.error('Error cleaning up intersection observer:', error);
        }
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    };
  }, [
    threshold,
    root,
    rootMargin,
    margin,
    thresholdPercentage,
    rootMarginOptions,
    options,
    onIntersectionChange,
    elementRef,
    onCleanup,
    onError,
    debug,
    performanceOptions,
  ]);

  return [elementRef as React.RefObject<T>, isInViewport];
}

// Common margins
export const margins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common threshold percentages
export const thresholdPercentages = {
  none: 0,
  quarter: 25,
  half: 50,
  threeQuarters: 75,
  full: 100,
};

// Common root margins
export const rootMargins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common options
export const options = {
  default: {
    trackVisibility: false,
    delay: 0,
  },
  delayed: {
    trackVisibility: false,
    delay: 100,
  },
  tracked: {
    trackVisibility: true,
    delay: 0,
  },
  trackedAndDelayed: {
    trackVisibility: true,
    delay: 100,
  },
};

// Common performance options
export const performanceOptions = {
  none: {
    measure: false,
    mark: false,
    log: false,
  },
  basic: {
    measure: true,
    mark: true,
    log: false,
  },
  full: {
    measure: true,
    mark: true,
    log: true,
  },
};
