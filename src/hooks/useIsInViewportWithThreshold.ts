import { useEffect, useRef, useState } from 'react';

interface UseIsInViewportWithThresholdProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  thresholdPercentage?: number;
}

export function useIsInViewportWithThreshold<T extends HTMLElement = HTMLElement>(
  options: UseIsInViewportWithThresholdProps = {}
): [React.RefObject<T>, boolean] {
  const { threshold = 0, root = null, rootMargin = '0px', thresholdPercentage = 0 } = options;

  const [isInViewport, setIsInViewport] = useState(false);
  const elementRef = useRef<T>(null);

  useEffect(() => {
    const element = elementRef.current;

    if (!element) {
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        const rect = entry.boundingClientRect;
        const rootRect = root
          ? root.getBoundingClientRect()
          : {
              top: 0,
              right: window.innerWidth,
              bottom: window.innerHeight,
              left: 0,
            };

        const elementArea = rect.width * rect.height;
        const intersectionArea =
          Math.max(0, Math.min(rect.right, rootRect.right) - Math.max(rect.left, rootRect.left)) *
          Math.max(0, Math.min(rect.bottom, rootRect.bottom) - Math.max(rect.top, rootRect.top));

        const intersectionPercentage = (intersectionArea / elementArea) * 100;

        setIsInViewport(intersectionPercentage >= thresholdPercentage);
      },
      {
        threshold,
        root,
        rootMargin,
      }
    );

    observer.observe(element);

    return () => {
      observer.unobserve(element);
    };
  }, [threshold, root, rootMargin, thresholdPercentage]);

  return [elementRef, isInViewport];
}

// Common threshold percentages
export const thresholdPercentages = {
  none: 0,
  quarter: 25,
  half: 50,
  threeQuarters: 75,
  full: 100,
};
