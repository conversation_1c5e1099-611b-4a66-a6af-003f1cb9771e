import { useEffect, useRef, useState } from 'react';

interface UseIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorProps {
  threshold?: number;
  root?: Element | null;
  rootMargin?: string;
  margin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  thresholdPercentage?: number;
  rootMargin?: {
    top?: number;
    right?: number;
    bottom?: number;
    left?: number;
  };
  options?: {
    trackVisibility?: boolean;
    delay?: number;
  };
  onIntersectionChange?: (isIntersecting: boolean, entry: IntersectionObserverEntry) => void;
  ref?: React.RefObject<HTMLElement>;
  initialState?: boolean;
  onCleanup?: () => void;
  onError?: (error: Error) => void;
}

export function useIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndError<
  T extends HTMLElement = HTMLElement,
>(
  props: UseIsInViewportWithMarginAndThresholdAndRootAndOptionsAndCallbackAndRefAndStateAndCleanupAndErrorProps = {}
): [React.RefObject<T>, boolean] {
  const {
    threshold = 0,
    root = null,
    rootMargin = '0px',
    margin = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    thresholdPercentage = 0,
    rootMargin: rootMarginOptions = {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
    },
    options = {
      trackVisibility: false,
      delay: 0,
    },
    onIntersectionChange,
    ref: externalRef,
    initialState = false,
    onCleanup,
    onError,
  } = props;

  const [isInViewport, setIsInViewport] = useState(initialState);
  const internalRef = useRef<T>(null);
  const elementRef = externalRef || internalRef;
  const timeoutRef = useRef<NodeJS.Timeout>();
  const isFirstRender = useRef(true);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    const element = elementRef.current;

    if (!element) {
      return;
    }

    try {
      observerRef.current = new IntersectionObserver(
        ([entry]) => {
          try {
            const rect = entry.boundingClientRect;
            const rootRect = root
              ? root.getBoundingClientRect()
              : {
                  top: 0,
                  right: window.innerWidth,
                  bottom: window.innerHeight,
                  left: 0,
                };

            const elementArea = rect.width * rect.height;
            const intersectionArea =
              Math.max(
                0,
                Math.min(rect.right + margin.right, rootRect.right + rootMarginOptions.right) -
                  Math.max(rect.left - margin.left, rootRect.left - rootMarginOptions.left)
              ) *
              Math.max(
                0,
                Math.min(rect.bottom + margin.bottom, rootRect.bottom + rootMarginOptions.bottom) -
                  Math.max(rect.top - margin.top, rootRect.top - rootMarginOptions.top)
              );

            const intersectionPercentage = (intersectionArea / elementArea) * 100;
            const isIntersecting = intersectionPercentage >= thresholdPercentage;

            if (options.delay) {
              if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
              }

              timeoutRef.current = setTimeout(() => {
                setIsInViewport(isIntersecting);
                onIntersectionChange?.(isIntersecting, entry);
              }, options.delay);
            } else {
              setIsInViewport(isIntersecting);
              onIntersectionChange?.(isIntersecting, entry);
            }
          } catch (error) {
            onError?.(error instanceof Error ? error : new Error(String(error)));
          }
        },
        {
          threshold,
          root,
          rootMargin,
          trackVisibility: options.trackVisibility,
        }
      );

      observerRef.current.observe(element);
    } catch (error) {
      onError?.(error instanceof Error ? error : new Error(String(error)));
    }

    return () => {
      try {
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current);
        }

        if (observerRef.current) {
          observerRef.current.unobserve(element);
          observerRef.current.disconnect();
        }

        onCleanup?.();
      } catch (error) {
        onError?.(error instanceof Error ? error : new Error(String(error)));
      }
    };
  }, [
    threshold,
    root,
    rootMargin,
    margin,
    thresholdPercentage,
    rootMarginOptions,
    options,
    onIntersectionChange,
    elementRef,
    onCleanup,
    onError,
  ]);

  return [elementRef as React.RefObject<T>, isInViewport];
}

// Common margins
export const margins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common threshold percentages
export const thresholdPercentages = {
  none: 0,
  quarter: 25,
  half: 50,
  threeQuarters: 75,
  full: 100,
};

// Common root margins
export const rootMargins = {
  none: {
    top: 0,
    right: 0,
    bottom: 0,
    left: 0,
  },
  small: {
    top: 10,
    right: 10,
    bottom: 10,
    left: 10,
  },
  medium: {
    top: 20,
    right: 20,
    bottom: 20,
    left: 20,
  },
  large: {
    top: 50,
    right: 50,
    bottom: 50,
    left: 50,
  },
};

// Common options
export const options = {
  default: {
    trackVisibility: false,
    delay: 0,
  },
  delayed: {
    trackVisibility: false,
    delay: 100,
  },
  tracked: {
    trackVisibility: true,
    delay: 0,
  },
  trackedAndDelayed: {
    trackVisibility: true,
    delay: 100,
  },
};
