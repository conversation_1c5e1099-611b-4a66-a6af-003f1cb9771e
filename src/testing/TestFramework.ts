/**
 * Комплексная система тестирования с поддержкой unit, integration, e2e тестов
 */

export interface TestConfig {
  timeout: number;
  retries: number;
  parallel: boolean;
  coverage: boolean;
  reporters: string[];
  environment: 'jsdom' | 'node' | 'browser';
  setupFiles: string[];
  teardownFiles: string[];
}

export interface TestCase {
  id: string;
  name: string;
  description: string;
  category: 'unit' | 'integration' | 'e2e' | 'performance' | 'accessibility' | 'visual';
  tags: string[];
  timeout?: number;
  retries?: number;
  skip?: boolean;
  only?: boolean;
  beforeEach?: () => Promise<void> | void;
  afterEach?: () => Promise<void> | void;
  test: () => Promise<void> | void;
}

export interface TestResult {
  id: string;
  name: string;
  status: 'passed' | 'failed' | 'skipped' | 'pending';
  duration: number;
  error?: Error;
  logs: string[];
  coverage?: CoverageReport;
  performance?: PerformanceMetrics;
  screenshots?: string[];
}

export interface TestSuite {
  id: string;
  name: string;
  description: string;
  tests: TestCase[];
  beforeAll?: () => Promise<void> | void;
  afterAll?: () => Promise<void> | void;
  beforeEach?: () => Promise<void> | void;
  afterEach?: () => Promise<void> | void;
}

export interface CoverageReport {
  lines: { total: number; covered: number; percentage: number };
  functions: { total: number; covered: number; percentage: number };
  branches: { total: number; covered: number; percentage: number };
  statements: { total: number; covered: number; percentage: number };
}

export interface PerformanceMetrics {
  memoryUsage: number;
  executionTime: number;
  cpuUsage: number;
  networkRequests: number;
}

export interface MockFunction<T extends (...args: any[]) => any> {
  (...args: Parameters<T>): ReturnType<T>;
  mockReturnValue(value: ReturnType<T>): MockFunction<T>;
  mockResolvedValue(value: Awaited<ReturnType<T>>): MockFunction<T>;
  mockRejectedValue(error: any): MockFunction<T>;
  mockImplementation(fn: T): MockFunction<T>;
  mockClear(): void;
  mockReset(): void;
  mockRestore(): void;
  calls: Parameters<T>[];
  results: Array<{ type: 'return' | 'throw'; value: any }>;
}

export class TestFramework {
  private config: TestConfig;
  private suites: Map<string, TestSuite> = new Map();
  private results: Map<string, TestResult> = new Map();
  private mocks: Map<string, MockFunction<any>> = new Map();
  private globalSetup: (() => Promise<void> | void)[] = [];
  private globalTeardown: (() => Promise<void> | void)[] = [];

  constructor(config: Partial<TestConfig> = {}) {
    this.config = {
      timeout: 5000,
      retries: 0,
      parallel: false,
      coverage: true,
      reporters: ['console', 'html'],
      environment: 'jsdom',
      setupFiles: [],
      teardownFiles: [],
      ...config,
    };
  }

  /**
   * Создает тестовый набор
   */
  describe(name: string, description: string, fn: () => void): TestSuite {
    const suite: TestSuite = {
      id: this.generateId(),
      name,
      description,
      tests: [],
    };

    // Временно устанавливаем текущий набор
    const currentSuite = suite;
    const originalDescribe = global.describe;
    const originalIt = global.it;
    const originalTest = global.test;
    const originalBeforeAll = global.beforeAll;
    const originalAfterAll = global.afterAll;
    const originalBeforeEach = global.beforeEach;
    const originalAfterEach = global.afterEach;

    // Переопределяем глобальные функции
    global.describe = this.describe.bind(this);
    global.it = global.test = this.createTestFunction(currentSuite);
    global.beforeAll = (fn: () => Promise<void> | void) => {
      currentSuite.beforeAll = fn;
    };
    global.afterAll = (fn: () => Promise<void> | void) => {
      currentSuite.afterAll = fn;
    };
    global.beforeEach = (fn: () => Promise<void> | void) => {
      currentSuite.beforeEach = fn;
    };
    global.afterEach = (fn: () => Promise<void> | void) => {
      currentSuite.afterEach = fn;
    };

    try {
      fn();
    } finally {
      // Восстанавливаем оригинальные функции
      global.describe = originalDescribe;
      global.it = originalIt;
      global.test = originalTest;
      global.beforeAll = originalBeforeAll;
      global.afterAll = originalAfterAll;
      global.beforeEach = originalBeforeEach;
      global.afterEach = originalAfterEach;
    }

    this.suites.set(suite.id, suite);
    return suite;
  }

  /**
   * Создает функцию для создания тестов
   */
  private createTestFunction(suite: TestSuite) {
    return (name: string, testFn: () => Promise<void> | void, options: Partial<TestCase> = {}) => {
      const test: TestCase = {
        id: this.generateId(),
        name,
        description: options.description || name,
        category: options.category || 'unit',
        tags: options.tags || [],
        timeout: options.timeout,
        retries: options.retries,
        skip: options.skip,
        only: options.only,
        beforeEach: options.beforeEach,
        afterEach: options.afterEach,
        test: testFn,
      };

      suite.tests.push(test);
    };
  }

  /**
   * Запускает все тесты
   */
  async runTests(filter?: {
    suites?: string[];
    tags?: string[];
    category?: string;
  }): Promise<Map<string, TestResult>> {
    console.log('🚀 Starting test execution...');

    // Выполняем глобальную настройку
    await this.runGlobalSetup();

    try {
      const suitesToRun = this.filterSuites(filter);

      if (this.config.parallel) {
        await this.runSuitesParallel(suitesToRun);
      } else {
        await this.runSuitesSequential(suitesToRun);
      }

      // Генерируем отчеты
      await this.generateReports();
    } finally {
      // Выполняем глобальную очистку
      await this.runGlobalTeardown();
    }

    return this.results;
  }

  /**
   * Запускает наборы тестов последовательно
   */
  private async runSuitesSequential(suites: TestSuite[]): Promise<void> {
    for (const suite of suites) {
      await this.runSuite(suite);
    }
  }

  /**
   * Запускает наборы тестов параллельно
   */
  private async runSuitesParallel(suites: TestSuite[]): Promise<void> {
    const promises = suites.map(suite => this.runSuite(suite));
    await Promise.all(promises);
  }

  /**
   * Запускает один набор тестов
   */
  private async runSuite(suite: TestSuite): Promise<void> {
    console.log(`📦 Running suite: ${suite.name}`);

    try {
      // Выполняем beforeAll
      if (suite.beforeAll) {
        await this.executeWithTimeout(suite.beforeAll, this.config.timeout);
      }

      // Запускаем тесты
      for (const test of suite.tests) {
        if (test.skip) {
          this.results.set(test.id, {
            id: test.id,
            name: test.name,
            status: 'skipped',
            duration: 0,
            logs: [],
          });
          continue;
        }

        await this.runTest(suite, test);
      }
    } finally {
      // Выполняем afterAll
      if (suite.afterAll) {
        try {
          await this.executeWithTimeout(suite.afterAll, this.config.timeout);
        } catch (error) {
          console.error(`Error in afterAll for suite ${suite.name}:`, error);
        }
      }
    }
  }

  /**
   * Запускает один тест
   */
  private async runTest(suite: TestSuite, test: TestCase): Promise<void> {
    const startTime = performance.now();
    const logs: string[] = [];
    let error: Error | undefined;
    let status: TestResult['status'] = 'passed';

    // Перехватываем логи
    const originalConsole = this.interceptConsole(logs);

    try {
      // Выполняем beforeEach
      if (suite.beforeEach) {
        await this.executeWithTimeout(suite.beforeEach, this.config.timeout);
      }
      if (test.beforeEach) {
        await this.executeWithTimeout(test.beforeEach, this.config.timeout);
      }

      // Выполняем тест с повторными попытками
      const retries = test.retries ?? this.config.retries;
      let lastError: Error | undefined;

      for (let attempt = 0; attempt <= retries; attempt++) {
        try {
          const timeout = test.timeout ?? this.config.timeout;
          await this.executeWithTimeout(test.test, timeout);
          lastError = undefined;
          break;
        } catch (err) {
          lastError = err as Error;
          if (attempt < retries) {
            console.log(`⚠️ Test ${test.name} failed, retrying (${attempt + 1}/${retries})...`);
          }
        }
      }

      if (lastError) {
        throw lastError;
      }
    } catch (err) {
      error = err as Error;
      status = 'failed';
    } finally {
      // Выполняем afterEach
      try {
        if (test.afterEach) {
          await this.executeWithTimeout(test.afterEach, this.config.timeout);
        }
        if (suite.afterEach) {
          await this.executeWithTimeout(suite.afterEach, this.config.timeout);
        }
      } catch (err) {
        console.error(`Error in afterEach for test ${test.name}:`, err);
      }

      // Восстанавливаем консоль
      this.restoreConsole(originalConsole);
    }

    const duration = performance.now() - startTime;

    const result: TestResult = {
      id: test.id,
      name: test.name,
      status,
      duration,
      error,
      logs,
    };

    // Собираем метрики производительности
    if (test.category === 'performance') {
      result.performance = await this.collectPerformanceMetrics();
    }

    // Собираем покрытие кода
    if (this.config.coverage) {
      result.coverage = await this.collectCoverage();
    }

    this.results.set(test.id, result);

    // Выводим результат
    const statusIcon = status === 'passed' ? '✅' : status === 'failed' ? '❌' : '⏭️';
    console.log(`${statusIcon} ${test.name} (${duration.toFixed(2)}ms)`);

    if (error) {
      console.error(`   Error: ${error.message}`);
    }
  }

  /**
   * Выполняет функцию с таймаутом
   */
  private async executeWithTimeout<T>(fn: () => Promise<T> | T, timeout: number): Promise<T> {
    return new Promise<T>((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`Test timed out after ${timeout}ms`));
      }, timeout);

      Promise.resolve(fn())
        .then(result => {
          clearTimeout(timer);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timer);
          reject(error);
        });
    });
  }

  /**
   * Перехватывает вывод консоли
   */
  private interceptConsole(logs: string[]): any {
    const original = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info,
    };

    console.log = (...args) => {
      logs.push(`LOG: ${args.join(' ')}`);
      original.log(...args);
    };

    console.error = (...args) => {
      logs.push(`ERROR: ${args.join(' ')}`);
      original.error(...args);
    };

    console.warn = (...args) => {
      logs.push(`WARN: ${args.join(' ')}`);
      original.warn(...args);
    };

    console.info = (...args) => {
      logs.push(`INFO: ${args.join(' ')}`);
      original.info(...args);
    };

    return original;
  }

  /**
   * Восстанавливает консоль
   */
  private restoreConsole(original: any): void {
    console.log = original.log;
    console.error = original.error;
    console.warn = original.warn;
    console.info = original.info;
  }

  /**
   * Создает mock функцию
   */
  createMock<T extends (...args: any[]) => any>(name?: string): MockFunction<T> {
    const calls: Parameters<T>[] = [];
    const results: Array<{ type: 'return' | 'throw'; value: any }> = [];
    let implementation: T | undefined;
    let returnValue: ReturnType<T> | undefined;
    let resolvedValue: Awaited<ReturnType<T>> | undefined;
    let rejectedValue: any;

    const mockFn = ((...args: Parameters<T>): ReturnType<T> => {
      calls.push(args);

      try {
        let result: ReturnType<T>;

        if (implementation) {
          result = implementation(...args);
        } else if (rejectedValue !== undefined) {
          throw rejectedValue;
        } else if (resolvedValue !== undefined) {
          result = Promise.resolve(resolvedValue) as ReturnType<T>;
        } else if (returnValue !== undefined) {
          result = returnValue;
        } else {
          result = undefined as ReturnType<T>;
        }

        results.push({ type: 'return', value: result });
        return result;
      } catch (error) {
        results.push({ type: 'throw', value: error });
        throw error;
      }
    }) as MockFunction<T>;

    mockFn.mockReturnValue = (value: ReturnType<T>) => {
      returnValue = value;
      return mockFn;
    };

    mockFn.mockResolvedValue = (value: Awaited<ReturnType<T>>) => {
      resolvedValue = value;
      return mockFn;
    };

    mockFn.mockRejectedValue = (error: any) => {
      rejectedValue = error;
      return mockFn;
    };

    mockFn.mockImplementation = (fn: T) => {
      implementation = fn;
      return mockFn;
    };

    mockFn.mockClear = () => {
      calls.length = 0;
      results.length = 0;
    };

    mockFn.mockReset = () => {
      mockFn.mockClear();
      implementation = undefined;
      returnValue = undefined;
      resolvedValue = undefined;
      rejectedValue = undefined;
    };

    mockFn.mockRestore = () => {
      // Восстановление оригинальной функции (если была заменена)
    };

    Object.defineProperty(mockFn, 'calls', {
      get: () => calls,
    });

    Object.defineProperty(mockFn, 'results', {
      get: () => results,
    });

    if (name) {
      this.mocks.set(name, mockFn);
    }

    return mockFn;
  }

  /**
   * Фильтрует наборы тестов
   */
  private filterSuites(filter?: {
    suites?: string[];
    tags?: string[];
    category?: string;
  }): TestSuite[] {
    let suites = Array.from(this.suites.values());

    if (filter?.suites) {
      suites = suites.filter(suite => filter.suites!.includes(suite.name));
    }

    if (filter?.tags || filter?.category) {
      suites = suites
        .map(suite => ({
          ...suite,
          tests: suite.tests.filter(test => {
            if (filter.category && test.category !== filter.category) {
              return false;
            }
            if (filter.tags && !filter.tags.some(tag => test.tags.includes(tag))) {
              return false;
            }
            return true;
          }),
        }))
        .filter(suite => suite.tests.length > 0);
    }

    return suites;
  }

  /**
   * Собирает метрики производительности
   */
  private async collectPerformanceMetrics(): Promise<PerformanceMetrics> {
    const memoryInfo = (performance as any).memory;

    return {
      memoryUsage: memoryInfo ? memoryInfo.usedJSHeapSize : 0,
      executionTime: performance.now(),
      cpuUsage: 0, // Недоступно в браузере
      networkRequests: 0, // Требует дополнительного отслеживания
    };
  }

  /**
   * Собирает покрытие кода
   */
  private async collectCoverage(): Promise<CoverageReport> {
    // Упрощенная реализация - в реальном проекте используйте Istanbul/NYC
    return {
      lines: { total: 100, covered: 85, percentage: 85 },
      functions: { total: 20, covered: 18, percentage: 90 },
      branches: { total: 30, covered: 25, percentage: 83.33 },
      statements: { total: 150, covered: 130, percentage: 86.67 },
    };
  }

  /**
   * Генерирует отчеты
   */
  private async generateReports(): Promise<void> {
    for (const reporter of this.config.reporters) {
      switch (reporter) {
        case 'console':
          this.generateConsoleReport();
          break;
        case 'html':
          await this.generateHtmlReport();
          break;
        case 'json':
          await this.generateJsonReport();
          break;
      }
    }
  }

  /**
   * Генерирует консольный отчет
   */
  private generateConsoleReport(): void {
    const results = Array.from(this.results.values());
    const passed = results.filter(r => r.status === 'passed').length;
    const failed = results.filter(r => r.status === 'failed').length;
    const skipped = results.filter(r => r.status === 'skipped').length;
    const total = results.length;

    console.log('\n📊 Test Results Summary:');
    console.log(`✅ Passed: ${passed}`);
    console.log(`❌ Failed: ${failed}`);
    console.log(`⏭️ Skipped: ${skipped}`);
    console.log(`📈 Total: ${total}`);
    console.log(`🎯 Success Rate: ${((passed / total) * 100).toFixed(2)}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      results
        .filter(r => r.status === 'failed')
        .forEach(result => {
          console.log(`   • ${result.name}: ${result.error?.message}`);
        });
    }
  }

  /**
   * Генерирует HTML отчет
   */
  private async generateHtmlReport(): Promise<void> {
    // Реализация генерации HTML отчета
    console.log('📄 HTML report would be generated here');
  }

  /**
   * Генерирует JSON отчет
   */
  private async generateJsonReport(): Promise<void> {
    // Реализация генерации JSON отчета
    console.log('📋 JSON report would be generated here');
  }

  /**
   * Выполняет глобальную настройку
   */
  private async runGlobalSetup(): Promise<void> {
    for (const setup of this.globalSetup) {
      await setup();
    }
  }

  /**
   * Выполняет глобальную очистку
   */
  private async runGlobalTeardown(): Promise<void> {
    for (const teardown of this.globalTeardown) {
      await teardown();
    }
  }

  /**
   * Добавляет глобальную настройку
   */
  addGlobalSetup(fn: () => Promise<void> | void): void {
    this.globalSetup.push(fn);
  }

  /**
   * Добавляет глобальную очистку
   */
  addGlobalTeardown(fn: () => Promise<void> | void): void {
    this.globalTeardown.push(fn);
  }

  /**
   * Генерирует уникальный ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Глобальный экземпляр
export const testFramework = new TestFramework();

// Экспорт утилит для тестирования
export const expect = {
  toBe: (actual: any, expected: any) => {
    if (actual !== expected) {
      throw new Error(`Expected ${actual} to be ${expected}`);
    }
  },
  toEqual: (actual: any, expected: any) => {
    if (JSON.stringify(actual) !== JSON.stringify(expected)) {
      throw new Error(`Expected ${JSON.stringify(actual)} to equal ${JSON.stringify(expected)}`);
    }
  },
  toThrow: (fn: () => any, expectedError?: string | RegExp) => {
    try {
      fn();
      throw new Error('Expected function to throw');
    } catch (error) {
      if (expectedError) {
        const message = (error as Error).message;
        if (typeof expectedError === 'string' && !message.includes(expectedError)) {
          throw new Error(`Expected error to contain "${expectedError}", got "${message}"`);
        }
        if (expectedError instanceof RegExp && !expectedError.test(message)) {
          throw new Error(`Expected error to match ${expectedError}, got "${message}"`);
        }
      }
    }
  },
};

// Глобальные функции для тестирования
declare global {
  var describe: (name: string, fn: () => void) => void;
  var it: (name: string, fn: () => Promise<void> | void) => void;
  var test: (name: string, fn: () => Promise<void> | void) => void;
  var beforeAll: (fn: () => Promise<void> | void) => void;
  var afterAll: (fn: () => Promise<void> | void) => void;
  var beforeEach: (fn: () => Promise<void> | void) => void;
  var afterEach: (fn: () => Promise<void> | void) => void;
}

// Enhanced testing utilities
export class TestUtils {
  /**
   * Create a comprehensive test report
   */
  public static generateComprehensiveReport(results: TestResult[]): {
    summary: TestSummary;
    coverage: CoverageReport;
    performance: PerformanceMetrics;
    quality: QualityMetrics;
    recommendations: string[];
  } {
    const summary = this.generateSummary(results);
    const coverage = this.aggregateCoverage(results);
    const performance = this.aggregatePerformance(results);
    const quality = this.calculateQualityMetrics(results);
    const recommendations = this.generateRecommendations(results);

    return {
      summary,
      coverage,
      performance,
      quality,
      recommendations,
    };
  }

  /**
   * Performance testing utilities
   */
  public static async measureComponentPerformance<T>(
    component: () => T,
    iterations = 100
  ): Promise<{
    averageTime: number;
    minTime: number;
    maxTime: number;
    memoryUsage: number;
  }> {
    const times: number[] = [];
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;

    for (let i = 0; i < iterations; i++) {
      const start = performance.now();
      component();
      const end = performance.now();
      times.push(end - start);
    }

    const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;

    return {
      averageTime: times.reduce((a, b) => a + b, 0) / times.length,
      minTime: Math.min(...times),
      maxTime: Math.max(...times),
      memoryUsage: finalMemory - initialMemory,
    };
  }

  /**
   * Accessibility testing utilities
   */
  public static async checkAccessibilityCompliance(
    element: Element,
    level: 'A' | 'AA' | 'AAA' = 'AA'
  ): Promise<{
    violations: Array<{
      rule: string;
      impact: 'minor' | 'moderate' | 'serious' | 'critical';
      description: string;
      elements: Element[];
    }>;
    passes: number;
    score: number;
  }> {
    const violations = [];
    let passes = 0;

    // Check for missing alt text
    const images = element.querySelectorAll('img');
    images.forEach(img => {
      if (!img.getAttribute('alt')) {
        violations.push({
          rule: 'img-alt',
          impact: 'serious' as const,
          description: 'Images must have alternative text',
          elements: [img],
        });
      } else {
        passes++;
      }
    });

    // Check for proper heading hierarchy
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    headings.forEach(heading => {
      const level = parseInt(heading.tagName.charAt(1));
      if (previousLevel > 0 && level > previousLevel + 1) {
        violations.push({
          rule: 'heading-order',
          impact: 'moderate' as const,
          description: 'Heading levels should not be skipped',
          elements: [heading],
        });
      } else {
        passes++;
      }
      previousLevel = level;
    });

    // Check for form labels
    const formElements = element.querySelectorAll('input, select, textarea');
    formElements.forEach(el => {
      const hasLabel =
        el.getAttribute('aria-label') ||
        el.getAttribute('aria-labelledby') ||
        element.querySelector(`label[for="${el.id}"]`);
      if (!hasLabel) {
        violations.push({
          rule: 'label',
          impact: 'serious' as const,
          description: 'Form elements must have labels',
          elements: [el],
        });
      } else {
        passes++;
      }
    });

    const totalChecks = violations.length + passes;
    const score = totalChecks > 0 ? (passes / totalChecks) * 100 : 100;

    return {
      violations,
      passes,
      score,
    };
  }

  /**
   * Visual regression testing utilities
   */
  public static async captureVisualSnapshot(
    element: Element,
    name: string
  ): Promise<{
    snapshot: string;
    dimensions: { width: number; height: number };
    timestamp: number;
  }> {
    const rect = element.getBoundingClientRect();

    return {
      snapshot: `visual-snapshot-${name}-${Date.now()}`,
      dimensions: {
        width: rect.width,
        height: rect.height,
      },
      timestamp: Date.now(),
    };
  }

  /**
   * Load testing utilities
   */
  public static async performLoadTest(
    testFunction: () => Promise<void>,
    options: {
      concurrent: number;
      duration: number;
      rampUp?: number;
    }
  ): Promise<{
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    requestsPerSecond: number;
    errors: Error[];
  }> {
    const { concurrent, duration, rampUp = 0 } = options;
    const results = {
      totalRequests: 0,
      successfulRequests: 0,
      failedRequests: 0,
      responseTimes: [] as number[],
      errors: [] as Error[],
    };

    const startTime = Date.now();
    const endTime = startTime + duration;
    const workers: Promise<void>[] = [];

    // Ramp up workers gradually
    for (let i = 0; i < concurrent; i++) {
      if (rampUp > 0) {
        await new Promise(resolve => setTimeout(resolve, rampUp / concurrent));
      }

      const worker = this.createLoadTestWorker(testFunction, endTime, results);
      workers.push(worker);
    }

    await Promise.all(workers);

    const averageResponseTime =
      results.responseTimes.length > 0
        ? results.responseTimes.reduce((a, b) => a + b, 0) / results.responseTimes.length
        : 0;

    const actualDuration = (Date.now() - startTime) / 1000;
    const requestsPerSecond = results.totalRequests / actualDuration;

    return {
      totalRequests: results.totalRequests,
      successfulRequests: results.successfulRequests,
      failedRequests: results.failedRequests,
      averageResponseTime,
      requestsPerSecond,
      errors: results.errors,
    };
  }

  private static async createLoadTestWorker(
    testFunction: () => Promise<void>,
    endTime: number,
    results: any
  ): Promise<void> {
    while (Date.now() < endTime) {
      const start = performance.now();
      results.totalRequests++;

      try {
        await testFunction();
        results.successfulRequests++;
      } catch (error) {
        results.failedRequests++;
        results.errors.push(error as Error);
      }

      const responseTime = performance.now() - start;
      results.responseTimes.push(responseTime);

      // Small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 10));
    }
  }

  private static generateSummary(results: TestResult[]): TestSummary {
    return {
      total: results.length,
      passed: results.filter(r => r.status === 'passed').length,
      failed: results.filter(r => r.status === 'failed').length,
      skipped: results.filter(r => r.status === 'skipped').length,
      pending: results.filter(r => r.status === 'pending').length,
      duration: results.reduce((sum, r) => sum + r.duration, 0),
      passRate:
        results.length > 0
          ? (results.filter(r => r.status === 'passed').length / results.length) * 100
          : 0,
    };
  }

  private static aggregateCoverage(results: TestResult[]): CoverageReport {
    const coverageResults = results.filter(r => r.coverage).map(r => r.coverage!);

    if (coverageResults.length === 0) {
      return {
        lines: { total: 0, covered: 0, percentage: 0 },
        functions: { total: 0, covered: 0, percentage: 0 },
        branches: { total: 0, covered: 0, percentage: 0 },
        statements: { total: 0, covered: 0, percentage: 0 },
      };
    }

    // Aggregate coverage data
    const totalLines = coverageResults.reduce((sum, c) => sum + c.lines.total, 0);
    const coveredLines = coverageResults.reduce((sum, c) => sum + c.lines.covered, 0);

    return {
      lines: {
        total: totalLines,
        covered: coveredLines,
        percentage: totalLines > 0 ? (coveredLines / totalLines) * 100 : 0,
      },
      functions: {
        total: coverageResults.reduce((sum, c) => sum + c.functions.total, 0),
        covered: coverageResults.reduce((sum, c) => sum + c.functions.covered, 0),
        percentage: 0, // Calculate based on totals
      },
      branches: {
        total: coverageResults.reduce((sum, c) => sum + c.branches.total, 0),
        covered: coverageResults.reduce((sum, c) => sum + c.branches.covered, 0),
        percentage: 0, // Calculate based on totals
      },
      statements: {
        total: coverageResults.reduce((sum, c) => sum + c.statements.total, 0),
        covered: coverageResults.reduce((sum, c) => sum + c.statements.covered, 0),
        percentage: 0, // Calculate based on totals
      },
    };
  }

  private static aggregatePerformance(results: TestResult[]): PerformanceMetrics {
    const performanceResults = results.filter(r => r.performance).map(r => r.performance!);

    if (performanceResults.length === 0) {
      return {
        renderTime: 0,
        memoryUsage: 0,
        bundleSize: 0,
        networkRequests: 0,
        cacheHitRate: 0,
        errorRate: 0,
      };
    }

    return {
      renderTime: Math.max(...performanceResults.map(p => p.renderTime)),
      memoryUsage: Math.max(...performanceResults.map(p => p.memoryUsage)),
      bundleSize: performanceResults.reduce((sum, p) => sum + p.bundleSize, 0),
      networkRequests: performanceResults.reduce((sum, p) => sum + p.networkRequests, 0),
      cacheHitRate:
        performanceResults.reduce((sum, p) => sum + p.cacheHitRate, 0) / performanceResults.length,
      errorRate:
        performanceResults.reduce((sum, p) => sum + p.errorRate, 0) / performanceResults.length,
    };
  }

  private static calculateQualityMetrics(results: TestResult[]): QualityMetrics {
    const passRate =
      results.length > 0
        ? (results.filter(r => r.status === 'passed').length / results.length) * 100
        : 0;
    const averageDuration =
      results.length > 0 ? results.reduce((sum, r) => sum + r.duration, 0) / results.length : 0;

    return {
      testReliability: passRate,
      codeQuality: Math.min(100, passRate + 10), // Simple heuristic
      maintainability: Math.max(0, 100 - (averageDuration / 1000) * 10), // Penalize slow tests
      testCoverage: 85, // Would be calculated from actual coverage data
      technicalDebt: Math.max(0, 100 - passRate), // Inverse of pass rate
    };
  }

  private static generateRecommendations(results: TestResult[]): string[] {
    const recommendations: string[] = [];
    const failedTests = results.filter(r => r.status === 'failed');
    const slowTests = results.filter(r => r.duration > 5000);

    if (failedTests.length > 0) {
      recommendations.push(`Fix ${failedTests.length} failing tests to improve reliability`);
    }

    if (slowTests.length > 0) {
      recommendations.push(`Optimize ${slowTests.length} slow tests (>5s) to improve performance`);
    }

    const passRate =
      results.length > 0
        ? (results.filter(r => r.status === 'passed').length / results.length) * 100
        : 0;
    if (passRate < 90) {
      recommendations.push('Improve test pass rate to at least 90%');
    }

    if (results.length < 50) {
      recommendations.push('Add more tests to improve coverage and confidence');
    }

    return recommendations;
  }
}
