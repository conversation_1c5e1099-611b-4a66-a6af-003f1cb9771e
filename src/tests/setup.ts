import '@testing-library/jest-dom';
import { TextDecoder, TextEncoder } from 'util';

import { configure } from '@testing-library/react';
import { vi } from 'vitest';

// Configure testing-library
configure({
  testIdAttribute: 'data-testid',
  asyncUtilTimeout: 5000,
});

// Mock Electron
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(name => `/mock/path/${name}`),
    getVersion: vi.fn(() => '1.0.0'),
    isPackaged: vi.fn(() => false),
    on: vi.fn(),
    once: vi.fn(),
    removeListener: vi.fn(),
  },
  BrowserWindow: {
    getAllWindows: vi.fn(() => []),
    getFocusedWindow: vi.fn(() => null),
    fromWebContents: vi.fn(() => null),
  },
  session: {
    defaultSession: {
      cookies: {
        get: vi.fn(),
        set: vi.fn(),
        remove: vi.fn(),
      },
      webRequest: {
        onHeadersReceived: vi.fn(),
        onBeforeRequest: vi.fn(),
      },
    },
  },
  net: {
    request: vi.fn(),
  },
  ipcMain: {
    on: vi.fn(),
    once: vi.fn(),
    handle: vi.fn(),
    removeHandler: vi.fn(),
  },
  ipcRenderer: {
    on: vi.fn(),
    once: vi.fn(),
    send: vi.fn(),
    invoke: vi.fn(),
    removeListener: vi.fn(),
  },
}));

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock crypto.randomUUID
Object.defineProperty(global.crypto, 'randomUUID', {
  value: vi.fn(() => 'mock-uuid'),
});

// Mock TextEncoder/TextDecoder
global.TextEncoder = TextEncoder;
global.TextDecoder = TextDecoder;

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
  length: 0,
  key: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// Mock fetch
global.fetch = vi.fn();

// Mock requestAnimationFrame
global.requestAnimationFrame = vi.fn(callback => setTimeout(callback, 0));
global.cancelAnimationFrame = vi.fn();

// Mock performance
global.performance = {
  now: vi.fn(() => Date.now()),
  mark: vi.fn(),
  measure: vi.fn(),
  clearMarks: vi.fn(),
  clearMeasures: vi.fn(),
  getEntriesByType: vi.fn(() => []),
  getEntriesByName: vi.fn(() => []),
  timeOrigin: Date.now(),
};

// Mock console methods
global.console = {
  ...console,
  error: vi.fn(),
  warn: vi.fn(),
  log: vi.fn(),
  info: vi.fn(),
  debug: vi.fn(),
};

// Enhanced test utilities
global.testUtils = {
  // Wait for condition with timeout
  waitFor: (condition: () => boolean, timeout = 5000): Promise<boolean> => {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      const check = () => {
        if (condition()) {
          resolve(true);
        } else if (Date.now() - startTime > timeout) {
          reject(new Error(`Timeout waiting for condition after ${timeout}ms`));
        } else {
          setTimeout(check, 100);
        }
      };
      check();
    });
  },

  // Create mock React component
  mockComponent: (name: string) => {
    return vi.fn().mockImplementation(({ children, ...props }) => {
      return {
        type: 'div',
        props: {
          'data-testid': `mock-${name}`,
          ...props,
          children,
        },
      };
    });
  },

  // Create mock event
  createMockEvent: (type: string, properties = {}) => {
    return {
      type,
      preventDefault: vi.fn(),
      stopPropagation: vi.fn(),
      stopImmediatePropagation: vi.fn(),
      target: { value: '' },
      currentTarget: { value: '' },
      bubbles: true,
      cancelable: true,
      defaultPrevented: false,
      ...properties,
    };
  },

  // Mock timer utilities
  advanceTimers: (ms: number) => {
    vi.advanceTimersByTime(ms);
  },

  // Mock network responses
  mockFetchResponse: (data: any, status = 200) => {
    (global.fetch as any).mockResolvedValueOnce({
      ok: status >= 200 && status < 300,
      status,
      statusText: status === 200 ? 'OK' : 'Error',
      json: vi.fn().mockResolvedValue(data),
      text: vi.fn().mockResolvedValue(JSON.stringify(data)),
      headers: new Map(),
    });
  },

  // Mock error response
  mockFetchError: (error: Error) => {
    (global.fetch as any).mockRejectedValueOnce(error);
  },

  // Performance testing utilities
  measurePerformance: async (fn: () => Promise<void> | void): Promise<number> => {
    const start = performance.now();
    await fn();
    return performance.now() - start;
  },

  // Memory leak detection
  detectMemoryLeaks: () => {
    const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
    return {
      check: () => {
        const currentMemory = (performance as any).memory?.usedJSHeapSize || 0;
        return currentMemory - initialMemory;
      },
    };
  },

  // Accessibility testing helpers
  checkAccessibility: (element: Element) => {
    const issues = [];

    // Check for missing alt text on images
    const images = element.querySelectorAll('img');
    images.forEach((img, index) => {
      if (!img.getAttribute('alt')) {
        issues.push(`Image at index ${index} missing alt attribute`);
      }
    });

    // Check for missing labels on form elements
    const formElements = element.querySelectorAll('input, select, textarea');
    formElements.forEach((el, index) => {
      const hasLabel =
        el.getAttribute('aria-label') ||
        el.getAttribute('aria-labelledby') ||
        element.querySelector(`label[for="${el.id}"]`);
      if (!hasLabel) {
        issues.push(`Form element at index ${index} missing accessible label`);
      }
    });

    // Check for proper heading hierarchy
    const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;
    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      if (previousLevel > 0 && level > previousLevel + 1) {
        issues.push(
          `Heading level skip detected at index ${index}: h${previousLevel} to h${level}`
        );
      }
      previousLevel = level;
    });

    return {
      isAccessible: issues.length === 0,
      issues,
    };
  },

  // Visual regression testing helpers
  captureScreenshot: async (element: Element): Promise<string> => {
    // Mock implementation - in real tests, use actual screenshot library
    return `screenshot-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  },

  // Component testing utilities
  renderWithProviders: (component: any, options = {}) => {
    // Mock implementation for rendering with all necessary providers
    return {
      ...component,
      rerender: vi.fn(),
      unmount: vi.fn(),
      debug: vi.fn(),
    };
  },

  // API testing utilities
  mockApiCall: (endpoint: string, response: any, delay = 0) => {
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(response);
      }, delay);
    });
  },

  // Error boundary testing
  triggerError: (component: any, error: Error) => {
    // Mock implementation for triggering errors in components
    throw error;
  },

  // State management testing
  mockStore: (initialState = {}) => {
    return {
      getState: vi.fn(() => initialState),
      dispatch: vi.fn(),
      subscribe: vi.fn(),
      replaceReducer: vi.fn(),
    };
  },

  // Internationalization testing
  mockI18n: (translations = {}) => {
    return {
      t: vi.fn((key: string) => translations[key] || key),
      changeLanguage: vi.fn(),
      language: 'en',
      languages: ['en', 'ru'],
    };
  },
};

// Performance monitoring for tests
const testPerformanceMonitor = {
  start: (testName: string) => {
    const startTime = performance.now();
    return {
      end: () => {
        const duration = performance.now() - startTime;
        if (duration > 1000) {
          console.warn(`Slow test detected: ${testName} took ${duration.toFixed(2)}ms`);
        }
        return duration;
      },
    };
  },
};

// Global test hooks
beforeEach(() => {
  // Reset all mocks
  vi.clearAllMocks();

  // Reset storage mocks
  localStorageMock.clear();
  sessionStorageMock.clear();

  // Reset fetch mock
  (global.fetch as any).mockClear();

  // Reset timers
  vi.useFakeTimers();
});

afterEach(() => {
  // Clean up timers
  vi.runOnlyPendingTimers();
  vi.useRealTimers();

  // Clean up any remaining async operations
  vi.clearAllTimers();
});

// Global error handling for tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Export test utilities for use in test files
export { testUtils, testPerformanceMonitor };
