import { createHash } from 'crypto';
import { promises as fs } from 'fs';
import { join } from 'path';

import { BrowserWindow, Session, WebContents, app, session } from 'electron';
import { Schema, validate } from 'jsonschema';

import { store } from '../store';
import {
  Extension,
  addExtensionError,
  installExtension,
  setAvailableExtensions,
  setExtensionEnabled,
  uninstallExtension,
  updateExtension,
  updateExtensionSettings,
  updateExtensionStatus,
} from '../store/slices/extensionsSlice';
import { errorHandler } from '../utils/errorHandler';
import { logger } from '../utils/logger';

interface ExtensionManifest {
  id: string;
  name: string;
  version: string;
  description: string;
  author: string;
  homepage?: string;
  icons?: Record<string, string>;
  permissions?: string[];
  contentScripts?: Array<{
    matches: string[];
    js?: string[];
    css?: string[];
  }>;
  background?: {
    serviceWorker?: string;
    persistent?: boolean;
  };
  options?: {
    page?: string;
    openInTab?: boolean;
  };
  compatibility: {
    minVersion: string;
    maxVersion?: string;
  };
  dependencies?: string[];
  conflicts?: string[];
  categories?: string[];
  tags?: string[];
  languages?: string[];
  license: string;
  privacyPolicy?: string;
  supportEmail?: string;
  repository?: string;
  changelog?: string;
  experimental?: boolean;
}

interface ExtensionProcess {
  manifest: ExtensionManifest;
  startedAt: number;
  backgroundProcess?: Worker;
}

interface ExtensionWorker {
  kill(): void;
  on(event: string, listener: (error: Error) => void): void;
  postMessage(message: any): void;
  terminate(): void;
}

interface ExtensionSession extends Session {
  webContents: WebContents;
}

export class ExtensionsService {
  private static instance: ExtensionsService;
  private extensionSchema: Record<string, unknown>;
  private updateCheckInterval: NodeJS.Timeout | null = null;
  private extensionProcesses: Map<string, ExtensionProcess> = new Map();

  private constructor() {
    this.extensionSchema = {};
    this.loadExtensionSchema();
    this.setupEventListeners();
  }

  public static getInstance(): ExtensionsService {
    if (!ExtensionsService.instance) {
      ExtensionsService.instance = new ExtensionsService();
    }
    return ExtensionsService.instance;
  }

  private async loadExtensionSchema(): Promise<void> {
    try {
      const schema = await fs.readFile(join(__dirname, 'schemas', 'extension.json'), 'utf-8');
      this.extensionSchema = JSON.parse(schema);
    } catch (error) {
      console.error('Failed to load extension schema:', error);
      throw new Error('Failed to initialize extension service: Schema loading failed');
    }
  }

  private setupEventListeners() {
    app.on('window-all-closed', () => {
      this.stopAllExtensions();
    });

    app.on('activate', () => {
      this.startEnabledExtensions();
    });
  }

  public async installExtension(extensionPath: string): Promise<Extension> {
    try {
      const manifest = await this.validateExtensionPackage(extensionPath);
      const extension: Extension = {
        id: manifest.id,
        name: manifest.name,
        version: manifest.version,
        description: manifest.description,
        author: manifest.author,
        permissions: manifest.permissions || [],
        path: extensionPath,
        enabled: true,
      };

      await this.copyDirectory(extensionPath, join(this.getExtensionsDir(), extension.id));
      return extension;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to install extension: ${error.message}`);
      }
      throw new Error('Failed to install extension: Unknown error occurred');
    }
  }

  public async uninstallExtension(extensionId: string): Promise<void> {
    try {
      const extension = await this.getExtension(extensionId);
      if (!extension) {
        throw new Error(`Extension ${extensionId} not found`);
      }

      await this.stopExtension(extensionId);
      await fs.rm(join(this.getExtensionsDir(), extensionId), { recursive: true, force: true });
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to uninstall extension: ${error.message}`);
      }
      throw new Error('Failed to uninstall extension: Unknown error occurred');
    }
  }

  public async updateExtension(extensionId: string): Promise<Extension> {
    try {
      updateExtensionStatus({ isUpdating: true });

      // Get update info
      const extension = store.getState().extensions.installed[extensionId];
      if (!extension.updateInfo) {
        throw new Error('No update available');
      }

      // Download update
      const updatePath = await this.downloadUpdate(extension);

      // Install update
      const updatedExtension = await this.installExtension(updatePath);

      // Clean up
      await fs.unlink(updatePath);

      updateExtensionStatus({ isUpdating: false });
      return updatedExtension;
    } catch (error: unknown) {
      updateExtensionStatus({ isUpdating: false });
      store.dispatch(
        addExtensionError({
          id: extensionId,
          code: 'UPDATE_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        })
      );
      throw error;
    }
  }

  public async startExtension(extension: Extension): Promise<void> {
    try {
      if (this.extensionProcesses.has(extension.id)) {
        return;
      }

      // Load extension
      const extensionPath = this.getExtensionPath(extension.id);
      const manifest = await this.loadManifest(extensionPath);

      // Start background process if exists
      if (manifest.background) {
        await this.startBackgroundProcess(extension, manifest.background);
      }

      // Inject content scripts
      if (manifest.contentScripts) {
        await this.injectContentScripts(extension, manifest.contentScripts);
      }

      this.extensionProcesses.set(extension.id, {
        manifest,
        startedAt: Date.now(),
      });

      store.dispatch(setExtensionEnabled({ id: extension.id, enabled: true }));
    } catch (error: unknown) {
      store.dispatch(
        addExtensionError({
          id: extension.id,
          code: 'START_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        })
      );
      throw error;
    }
  }

  public async stopExtension(extensionId: string): Promise<void> {
    try {
      const process = this.extensionProcesses.get(extensionId);
      if (!process) {
        return;
      }

      // Stop background process
      if (process.backgroundProcess) {
        process.backgroundProcess.kill();
      }

      // Remove content scripts
      await this.removeContentScripts(extensionId);

      this.extensionProcesses.delete(extensionId);
      store.dispatch(setExtensionEnabled({ id: extensionId, enabled: false }));
    } catch (error: unknown) {
      store.dispatch(
        addExtensionError({
          id: extensionId,
          code: 'STOP_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        })
      );
      throw error;
    }
  }

  public async checkForUpdates(): Promise<void> {
    try {
      updateExtensionStatus({ isChecking: true });

      const extensions = store.getState().extensions.installed;
      const updates: Extension[] = [];

      for (const ext of Object.values(extensions)) {
        const extension = ext as Extension;
        try {
          const updateInfo = await this.fetchUpdateInfo(extension);
          if (updateInfo) {
            const updatedExtension: Extension = {
              ...extension,
              updateAvailable: true,
              updateInfo,
            };
            updates.push(updatedExtension);
          }
        } catch (error: unknown) {
          logger.error(`Failed to check updates for ${extension.id}:`, error);
        }
      }

      // Update store
      updates.forEach(extension => {
        store.dispatch(
          updateExtension({
            id: extension.id,
            changes: {
              updateAvailable: true,
              updateInfo: extension.updateInfo,
            },
          })
        );
      });

      updateExtensionStatus({
        isChecking: false,
        lastCheck: Date.now(),
      });
    } catch (error: unknown) {
      updateExtensionStatus({ isChecking: false });
      store.dispatch(
        addExtensionError({
          id: 'update-check',
          code: 'CHECK_FAILED',
          message: error instanceof Error ? error.message : 'Unknown error occurred',
        })
      );
      throw error;
    }
  }

  public startUpdateChecker(): void {
    const { updateCheckInterval } = store.getState().extensions.settings;
    this.updateCheckInterval = setInterval(() => {
      this.checkForUpdates();
    }, updateCheckInterval);
  }

  public stopUpdateChecker(): void {
    if (this.updateCheckInterval) {
      clearInterval(this.updateCheckInterval);
      this.updateCheckInterval = null;
    }
  }

  private validateManifest(manifest: ExtensionManifest): boolean {
    // Basic validation
    const requiredFields = [
      'id',
      'name',
      'version',
      'description',
      'author',
      'compatibility',
      'license',
    ];
    for (const field of requiredFields) {
      if (!manifest[field as keyof ExtensionManifest]) {
        throw new Error(`Missing required field: ${field}`);
      }
    }

    // Version format validation
    const versionRegex = /^\d+\.\d+\.\d+$/;
    if (!versionRegex.test(manifest.version)) {
      throw new Error('Invalid version format. Expected format: x.y.z');
    }

    // Compatibility validation
    if (!versionRegex.test(manifest.compatibility.minVersion)) {
      throw new Error('Invalid minVersion format. Expected format: x.y.z');
    }
    if (
      manifest.compatibility.maxVersion &&
      !versionRegex.test(manifest.compatibility.maxVersion)
    ) {
      throw new Error('Invalid maxVersion format. Expected format: x.y.z');
    }

    return true;
  }

  private async validateExtensionPackage(path: string): Promise<ExtensionManifest> {
    try {
      const manifestPath = join(path, 'manifest.json');
      const manifestContent = await fs.readFile(manifestPath, 'utf-8');
      const manifest = JSON.parse(manifestContent) as ExtensionManifest;

      await this.checkCompatibility(manifest);
      await this.verifySignature(manifest);

      return manifest;
    } catch (error) {
      if (error instanceof Error) {
        throw new Error(`Failed to validate extension package: ${error.message}`);
      }
      throw new Error('Failed to validate extension package: Unknown error occurred');
    }
  }

  private async checkCompatibility(manifest: ExtensionManifest): Promise<void> {
    const requiredFields = ['id', 'name', 'version', 'description', 'author'];
    const missingFields = requiredFields.filter(field => !(field in manifest));

    if (missingFields.length > 0) {
      throw new Error(`Missing required fields in manifest: ${missingFields.join(', ')}`);
    }

    if (manifest.minVersion && !this.isVersionCompatible(manifest.minVersion)) {
      throw new Error(`Extension requires minimum version ${manifest.minVersion}`);
    }
  }

  private async verifySignature(manifest: ExtensionManifest): Promise<void> {
    if (!manifest.signature) {
      throw new Error('Extension signature is required');
    }

    // Implement signature verification logic here
    // This is a placeholder for actual signature verification
    const isValid = await this.verifyExtensionSignature(manifest);
    if (!isValid) {
      throw new Error('Invalid extension signature');
    }
  }

  private async verifyExtensionSignature(manifest: ExtensionManifest): Promise<boolean> {
    // Implement actual signature verification logic
    // This is a placeholder that always returns true
    return true;
  }

  private isVersionCompatible(requiredVersion: string): boolean {
    // Implement version compatibility check
    // This is a placeholder that always returns true
    return true;
  }

  private async copyExtensionFiles(sourcePath: string, extensionId: string): Promise<string> {
    const targetPath = this.getExtensionPath(extensionId);
    await fs.mkdir(targetPath, { recursive: true });
    await this.copyDirectory(sourcePath, targetPath);
    return targetPath;
  }

  private async copyDirectory(source: string, target: string): Promise<void> {
    const entries = await fs.readdir(source, { withFileTypes: true });

    for (const entry of entries) {
      const sourcePath = join(source, entry.name);
      const targetPath = join(target, entry.name);

      if (entry.isDirectory()) {
        await fs.mkdir(targetPath, { recursive: true });
        await this.copyDirectory(sourcePath, targetPath);
      } else {
        await fs.copyFile(sourcePath, targetPath);
      }
    }
  }

  private getExtensionPath(extensionId: string): string {
    return join(app.getPath('userData'), 'extensions', extensionId);
  }

  private async loadExtension(path: string): Promise<Extension> {
    const manifest = await this.loadManifest(path);
    return {
      id: manifest.id,
      name: manifest.name,
      version: manifest.version,
      description: manifest.description,
      author: manifest.author,
      homepage: manifest.homepage,
      icon: manifest.icons?.['128'],
      permissions: manifest.permissions || [],
      contentScripts: manifest.contentScripts || [],
      background: manifest.background,
      options: manifest.options,
      settings: {},
      enabled: true,
      installedAt: Date.now(),
      updatedAt: Date.now(),
      size: await this.calculateExtensionSize(path),
      rating: 0,
      downloads: 0,
      reviews: 0,
      compatibility: manifest.compatibility,
      dependencies: manifest.dependencies,
      conflicts: manifest.conflicts,
      categories: manifest.categories || [],
      tags: manifest.tags || [],
      languages: manifest.languages || ['en'],
      license: manifest.license,
      privacyPolicy: manifest.privacyPolicy,
      supportEmail: manifest.supportEmail,
      repository: manifest.repository,
      changelog: manifest.changelog,
      isVerified: false,
      isOfficial: false,
      isExperimental: manifest.experimental || false,
      isDeprecated: false,
      isBlocked: false,
      lastCheck: Date.now(),
      updateAvailable: false,
    };
  }

  private async loadManifest(path: string): Promise<ExtensionManifest> {
    const manifestPath = join(path, 'manifest.json');
    const manifestContent = await fs.readFile(manifestPath, 'utf-8');
    return JSON.parse(manifestContent) as ExtensionManifest;
  }

  private async calculateExtensionSize(path: string): Promise<number> {
    let size = 0;
    const entries = await fs.readdir(path, { withFileTypes: true });

    for (const entry of entries) {
      const entryPath = join(path, entry.name);
      if (entry.isDirectory()) {
        size += await this.calculateExtensionSize(entryPath);
      } else {
        const stats = await fs.stat(entryPath);
        size += stats.size;
      }
    }

    return size;
  }

  private async startBackgroundProcess(
    extension: Extension,
    background: ExtensionManifest['background']
  ): Promise<void> {
    if (!background?.serviceWorker) {
      return;
    }

    const workerPath = join(this.getExtensionPath(extension.id), background.serviceWorker);
    const worker = new Worker(workerPath) as unknown as ExtensionWorker;

    worker.on('error', (error: Error) => {
      store.dispatch(
        addExtensionError({
          id: extension.id,
          code: 'WORKER_ERROR',
          message: error.message,
        })
      );
    });

    const process = this.extensionProcesses.get(extension.id);
    if (process) {
      process.backgroundProcess = worker as unknown as Worker;
      this.extensionProcesses.set(extension.id, process);
    }
  }

  private async injectContentScripts(
    extension: Extension,
    contentScripts: ExtensionManifest['contentScripts']
  ): Promise<void> {
    if (!contentScripts) return;

    const extSession = session.defaultSession as unknown as ExtensionSession;

    for (const script of contentScripts) {
      for (const match of script.matches) {
        await extSession.webContents.executeJavaScript(`
          if (script.js) {
            script.js.forEach(function(jsFile) {
              const script = document.createElement('script');
              script.src = 'extensions://${extension.id}/' + jsFile;
              document.head.appendChild(script);
            });
          }
          if (script.css) {
            script.css.forEach(function(cssFile) {
              const link = document.createElement('link');
              link.rel = 'stylesheet';
              link.href = 'extensions://${extension.id}/' + cssFile;
              document.head.appendChild(link);
            });
          }
        `);
      }
    }
  }

  private async removeContentScripts(extensionId: string): Promise<void> {
    const extSession = session.defaultSession as unknown as ExtensionSession;
    await extSession.webContents.executeJavaScript(`
      document.querySelectorAll('script[src^="extensions://${extensionId}/"]').forEach(script => script.remove());
      document.querySelectorAll('link[href^="extensions://${extensionId}/"]').forEach(link => link.remove());
    `);
  }

  private async fetchUpdateInfo(extension: Extension): Promise<any> {
    // Implement update check logic
    // This would typically involve checking a remote repository
    // or extension store for available updates
    return null;
  }

  private async downloadUpdate(extension: Extension): Promise<string> {
    // Implement update download logic
    // This would typically involve downloading the new version
    // from a remote repository or extension store
    return '';
  }

  private compareVersions(v1: string, v2: string): number {
    const parts1 = v1.split('.').map(Number);
    const parts2 = v2.split('.').map(Number);

    for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
      const part1 = parts1[i] || 0;
      const part2 = parts2[i] || 0;

      if (part1 > part2) return 1;
      if (part1 < part2) return -1;
    }

    return 0;
  }

  private startEnabledExtensions(): void {
    const extensions = store.getState().extensions.installed;
    Object.values(extensions)
      .filter(ext => (ext as Extension).enabled)
      .forEach(ext => this.startExtension(ext as Extension));
  }

  private stopAllExtensions(): void {
    const extensions = store.getState().extensions.installed;
    Object.keys(extensions).forEach(id => this.stopExtension(id));
  }
}

export const extensionsService = ExtensionsService.getInstance();
