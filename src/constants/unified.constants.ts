/**
 * Unified Constants
 * Consolidates all duplicate constants, enums, and configuration values
 */

// ============================================================================
// APPLICATION CONSTANTS
// ============================================================================

export const APP = {
  NAME: 'A14-Browser',
  VERSION: '1.0.0',
  DESCRIPTION: 'Advanced web browser with enhanced security and performance',
  AUTHOR: 'A14 Team',
  LICENSE: 'MIT',
  HOMEPAGE: 'https://a14browser.com',
  REPOSITORY: 'https://github.com/a14team/a14-browser',
} as const;

// ============================================================================
// API ENDPOINTS
// ============================================================================

export const API_ENDPOINTS = {
  // Authentication
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    VERIFY: '/auth/verify',
    RESET_PASSWORD: '/auth/reset-password',
  },
  
  // User Management
  USER: {
    PROFILE: '/user/profile',
    SETTINGS: '/user/settings',
    PREFERENCES: '/user/preferences',
    AVATAR: '/user/avatar',
    DELETE: '/user/delete',
  },
  
  // Security
  SECURITY: {
    ENCRYPTION: '/security/encryption',
    DECRYPTION: '/security/decryption',
    TOKEN: '/security/token',
    SCAN: '/security/scan',
    REPORT: '/security/report',
  },
  
  // Analytics
  ANALYTICS: {
    EVENTS: '/analytics/events',
    METRICS: '/analytics/metrics',
    REPORTS: '/analytics/reports',
    TRACKING: '/analytics/tracking',
  },
  
  // Extensions
  EXTENSIONS: {
    LIST: '/extensions',
    INSTALL: '/extensions/install',
    UNINSTALL: '/extensions/uninstall',
    UPDATE: '/extensions/update',
    SEARCH: '/extensions/search',
  },
  
  // Sync
  SYNC: {
    BOOKMARKS: '/sync/bookmarks',
    HISTORY: '/sync/history',
    SETTINGS: '/sync/settings',
    EXTENSIONS: '/sync/extensions',
    TABS: '/sync/tabs',
  },
} as const;

// External API URLs
export const EXTERNAL_APIS = {
  EXTENSIONS: 'https://api.a14browser.com/extensions',
  UPDATES: 'https://api.a14browser.com/updates',
  SYNC: 'https://api.a14browser.com/sync',
  FEEDBACK: 'https://api.a14browser.com/feedback',
  TELEMETRY: 'https://api.a14browser.com/telemetry',
} as const;

// ============================================================================
// SECURITY CONSTANTS
// ============================================================================

export const SECURITY = {
  ENCRYPTION: {
    ALGORITHM: 'aes-256-gcm',
    KEY_SIZE: 32,
    ITERATIONS: 100000,
    SALT_SIZE: 16,
  },
  JWT: {
    EXPIRES_IN: '1h',
    REFRESH_EXPIRES_IN: '7d',
    ALGORITHM: 'HS256',
  },
  HASHING: {
    ALGORITHM: 'sha256',
    ROUNDS: 12,
  },
  CSP: {
    DEFAULT_SRC: "'self'",
    SCRIPT_SRC: "'self' 'unsafe-inline'",
    STYLE_SRC: "'self' 'unsafe-inline'",
    IMG_SRC: "'self' data: https:",
  },
} as const;

// ============================================================================
// PERFORMANCE CONSTANTS
// ============================================================================

export const PERFORMANCE = {
  CACHE: {
    DEFAULT_TTL: 300000, // 5 minutes
    MAX_SIZE: 100 * 1024 * 1024, // 100MB
    MAX_ENTRIES: 10000,
    CLEANUP_INTERVAL: 60000, // 1 minute
  },
  MEMORY: {
    MAX_HEAP_SIZE: 2048, // MB
    GC_THRESHOLD: 80, // percentage
    WARNING_THRESHOLD: 90, // percentage
  },
  NETWORK: {
    TIMEOUT: 30000, // 30 seconds
    RETRY_ATTEMPTS: 3,
    RETRY_DELAY: 1000, // 1 second
    MAX_CONCURRENT: 10,
  },
} as const;

// ============================================================================
// UI CONSTANTS
// ============================================================================

export const UI = {
  BREAKPOINTS: {
    XS: 480,
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    XXL: 1536,
  },
  Z_INDEX: {
    DROPDOWN: 1000,
    STICKY: 1020,
    FIXED: 1030,
    MODAL_BACKDROP: 1040,
    MODAL: 1050,
    POPOVER: 1060,
    TOOLTIP: 1070,
    NOTIFICATION: 1080,
  },
  ANIMATION: {
    DURATION_FAST: 150,
    DURATION_BASE: 250,
    DURATION_SLOW: 350,
    EASING: 'cubic-bezier(0.4, 0, 0.2, 1)',
  },
  LAYOUT: {
    TOOLBAR_HEIGHT: 48,
    SIDEBAR_WIDTH: 240,
    TAB_HEIGHT: 36,
    ADDRESS_BAR_HEIGHT: 40,
    STATUS_BAR_HEIGHT: 24,
  },
} as const;

// ============================================================================
// BROWSER CONSTANTS
// ============================================================================

export const BROWSER = {
  USER_AGENT: 'A14-Browser/1.0.0',
  DEFAULT_SEARCH_ENGINE: 'https://www.google.com/search?q=',
  NEW_TAB_URL: 'a14://newtab',
  SETTINGS_URL: 'a14://settings',
  EXTENSIONS_URL: 'a14://extensions',
  HISTORY_URL: 'a14://history',
  BOOKMARKS_URL: 'a14://bookmarks',
} as const;

// ============================================================================
// EXTENSION CONSTANTS
// ============================================================================

export const EXTENSIONS = {
  CATEGORIES: {
    PRODUCTIVITY: 'productivity',
    PRIVACY: 'privacy',
    SOCIAL: 'social',
    SHOPPING: 'shopping',
    NEWS: 'news',
    ENTERTAINMENT: 'entertainment',
    TOOLS: 'tools',
    DEVELOPER: 'developer',
    ACCESSIBILITY: 'accessibility',
    EDUCATION: 'education',
  },
  PERMISSIONS: {
    TABS: 'tabs',
    ACTIVE_TAB: 'activeTab',
    STORAGE: 'storage',
    BOOKMARKS: 'bookmarks',
    HISTORY: 'history',
    COOKIES: 'cookies',
    WEB_REQUEST: 'webRequest',
    WEB_NAVIGATION: 'webNavigation',
    NOTIFICATIONS: 'notifications',
    CONTEXT_MENUS: 'contextMenus',
  },
  MANIFEST_VERSION: 3,
} as const;

// ============================================================================
// TIMEOUTS AND INTERVALS
// ============================================================================

export const TIMEOUTS = {
  EXTENSION_LOAD: 5000,
  SYNC: 30000,
  UPDATE_CHECK: 3600000, // 1 hour
  TAB_SLEEP: 300000, // 5 minutes
  CACHE_CLEANUP: 86400000, // 24 hours
  SESSION_TIMEOUT: 1800000, // 30 minutes
  HEARTBEAT: 30000, // 30 seconds
} as const;

// ============================================================================
// LIMITS
// ============================================================================

export const LIMITS = {
  MAX_TABS: 100,
  MAX_EXTENSIONS: 50,
  MAX_BOOKMARKS: 10000,
  MAX_HISTORY: 100000,
  MAX_DOWNLOADS: 1000,
  MAX_PROFILES: 10,
  MAX_THEMES: 100,
  MAX_SEARCH_RESULTS: 1000,
  MAX_FILE_SIZE: 100 * 1024 * 1024, // 100MB
  MAX_UPLOAD_SIZE: 50 * 1024 * 1024, // 50MB
} as const;

// ============================================================================
// REGULAR EXPRESSIONS
// ============================================================================

export const REGEX = {
  URL: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  VERSION: /^\d+\.\d+\.\d+$/,
  IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  IPV6: /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
  DOMAIN: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9](?:\.[a-zA-Z]{2,})+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
} as const;

// ============================================================================
// FILE TYPES AND MIME TYPES
// ============================================================================

export const FILE_TYPES = {
  IMAGE: ['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'bmp', 'ico'],
  DOCUMENT: ['pdf', 'doc', 'docx', 'txt', 'rtf', 'odt', 'pages'],
  AUDIO: ['mp3', 'wav', 'ogg', 'm4a', 'flac', 'aac'],
  VIDEO: ['mp4', 'webm', 'mov', 'avi', 'mkv', 'wmv', 'flv'],
  ARCHIVE: ['zip', 'rar', '7z', 'tar', 'gz', 'bz2', 'xz'],
  CODE: ['js', 'ts', 'html', 'css', 'json', 'xml', 'py', 'java', 'cpp'],
} as const;

export const MIME_TYPES = {
  HTML: 'text/html',
  CSS: 'text/css',
  JAVASCRIPT: 'application/javascript',
  JSON: 'application/json',
  XML: 'application/xml',
  PDF: 'application/pdf',
  IMAGE: 'image/*',
  AUDIO: 'audio/*',
  VIDEO: 'video/*',
  FONT: 'font/*',
  TEXT: 'text/plain',
  BINARY: 'application/octet-stream',
} as const;

// ============================================================================
// HTTP STATUS CODES
// ============================================================================

export const HTTP_STATUS = {
  // Success
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  
  // Redirection
  MOVED_PERMANENTLY: 301,
  FOUND: 302,
  NOT_MODIFIED: 304,
  
  // Client Error
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  
  // Server Error
  INTERNAL_SERVER_ERROR: 500,
  NOT_IMPLEMENTED: 501,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
  GATEWAY_TIMEOUT: 504,
} as const;

// ============================================================================
// IPC CHANNELS
// ============================================================================

export const IPC_CHANNELS = {
  // Settings
  GET_ALL_SETTINGS: 'settings:getAll',
  SET_SETTING: 'settings:set',
  RESET_SETTINGS: 'settings:reset',
  
  // Bookmarks
  GET_ALL_BOOKMARKS: 'bookmarks:getAll',
  ADD_BOOKMARK: 'bookmarks:add',
  REMOVE_BOOKMARK: 'bookmarks:remove',
  UPDATE_BOOKMARK: 'bookmarks:update',
  
  // Extensions
  GET_EXTENSIONS: 'extensions:getAll',
  INSTALL_EXTENSION: 'extensions:install',
  UNINSTALL_EXTENSION: 'extensions:uninstall',
  ENABLE_EXTENSION: 'extensions:enable',
  DISABLE_EXTENSION: 'extensions:disable',
  
  // Updates
  UPDATE_AVAILABLE: 'update:available',
  UPDATE_DOWNLOADED: 'update:downloaded',
  QUIT_AND_INSTALL_UPDATE: 'update:quitAndInstall',
  CHECK_FOR_UPDATES: 'update:check',
  
  // Window Management
  MINIMIZE_WINDOW: 'window:minimize',
  MAXIMIZE_WINDOW: 'window:maximize',
  CLOSE_WINDOW: 'window:close',
  TOGGLE_FULLSCREEN: 'window:toggleFullscreen',
  
  // Security
  ENCRYPT_DATA: 'security:encrypt',
  DECRYPT_DATA: 'security:decrypt',
  SCAN_FILE: 'security:scanFile',
  
  // Performance
  GET_PERFORMANCE_METRICS: 'performance:getMetrics',
  OPTIMIZE_PERFORMANCE: 'performance:optimize',
  CLEAR_CACHE: 'performance:clearCache',
} as const;

// ============================================================================
// EVENTS
// ============================================================================

export const EVENTS = {
  // Application
  APP_READY: 'app:ready',
  APP_QUIT: 'app:quit',
  APP_ERROR: 'app:error',
  
  // Window
  WINDOW_CREATED: 'window:created',
  WINDOW_CLOSED: 'window:closed',
  WINDOW_FOCUSED: 'window:focused',
  WINDOW_BLURRED: 'window:blurred',
  
  // Tab
  TAB_CREATED: 'tab:created',
  TAB_UPDATED: 'tab:updated',
  TAB_REMOVED: 'tab:removed',
  TAB_ACTIVATED: 'tab:activated',
  
  // Extension
  EXTENSION_INSTALLED: 'extension:installed',
  EXTENSION_UNINSTALLED: 'extension:uninstalled',
  EXTENSION_ENABLED: 'extension:enabled',
  EXTENSION_DISABLED: 'extension:disabled',
  
  // Security
  SECURITY_THREAT_DETECTED: 'security:threatDetected',
  SECURITY_SCAN_COMPLETE: 'security:scanComplete',
  
  // Performance
  PERFORMANCE_WARNING: 'performance:warning',
  MEMORY_THRESHOLD_EXCEEDED: 'performance:memoryThresholdExceeded',
} as const;

// ============================================================================
// LEGACY ALIASES FOR BACKWARD COMPATIBILITY
// ============================================================================

// Legacy API endpoints
export const API = EXTERNAL_APIS;

// Legacy extension categories
export const EXTENSION_CATEGORIES = EXTENSIONS.CATEGORIES;

// Legacy IPC channels
export const IpcChannels = IPC_CHANNELS;

// Legacy app constants
export const APP_NAME = APP.NAME;
export const APP_VERSION = APP.VERSION;
export const APP_DESCRIPTION = APP.DESCRIPTION;
