import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import { persistReducer, persistStore } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import { encryptTransform } from 'redux-persist-transform-encrypt';

import { browserApi } from '../services/api/browserApi';
import { extensionsApi } from '../services/api/extensionsApi';
import { syncApi } from '../services/api/syncApi';
import { logger } from '../utils/logger';

// Import slices
import accessibilityReducer from './slices/accessibilitySlice';
import bookmarksReducer from './slices/bookmarksSlice';
import browserReducer from './slices/browserSlice';
import downloadsReducer from './slices/downloadsSlice';
import extensionsReducer from './slices/extensionsSlice';
import historyReducer from './slices/historySlice';
import notificationReducer from './slices/notificationSlice';
import notificationsReducer from './slices/notificationsSlice';
import performanceReducer from './slices/performanceSlice';
import securityReducer from './slices/securitySlice';
import settingsReducer from './slices/settingsSlice';
import syncReducer from './slices/syncSlice';
import tabsReducer from './slices/tabsSlice';
import themeReducer from './slices/themeSlice';
import uiReducer from './slices/uiSlice';
import userReducer from './slices/userSlice';

// Import API services

// Configure encryption for sensitive data
const encryptor = encryptTransform({
  secretKey: process.env.REDUX_ENCRYPTION_KEY || 'default-key-change-in-production',
  onError: error => {
    logger.error('Redux encryption error:', error);
  },
});

// Configure persistence
const persistConfig = {
  key: 'root',
  storage,
  transforms: [encryptor],
  whitelist: ['settings', 'bookmarks', 'history', 'user'], // Only persist these reducers
  blacklist: ['browser', 'tabs', 'downloads'], // Don't persist these reducers
};

// Combine reducers
const rootReducer = {
  browser: browserReducer,
  settings: settingsReducer,
  tabs: tabsReducer,
  bookmarks: bookmarksReducer,
  history: historyReducer,
  downloads: downloadsReducer,
  extensions: extensionsReducer,
  security: securityReducer,
  user: userReducer,
  ui: uiReducer,
  performance: performanceReducer,
  sync: syncReducer,
  notifications: notificationsReducer,
  theme: themeReducer,
  accessibility: accessibilityReducer,
  notifications: notificationReducer,
  [browserApi.reducerPath]: browserApi.reducer,
  [extensionsApi.reducerPath]: extensionsApi.reducer,
  [syncApi.reducerPath]: syncApi.reducer,
};

// Configure store
export const store = configureStore({
  reducer: persistReducer(persistConfig, rootReducer),
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: false,
      immutableCheck: false,
    }),
  devTools: process.env.NODE_ENV !== 'production',
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

// Create persistor
export const persistor = persistStore(store);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Export store
export default store;
