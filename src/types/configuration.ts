export type ConfigurationType = 'system' | 'user' | 'application' | 'custom';
export type ConfigurationStatus = 'active' | 'inactive' | 'deprecated' | 'deleted';
export type ConfigurationScope = 'global' | 'user' | 'group' | 'custom';

export interface ConfigurationConfig {
  management: {
    enabled: boolean;
    types: ConfigurationType[];
    scopes: ConfigurationScope[];
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        pattern: string;
        message: string;
      }[];
    };
  };
  storage: {
    type: 'database' | 'file' | 's3' | 'custom';
    encryption?: {
      enabled: boolean;
      algorithm: string;
      key?: string;
    };
    backup?: {
      enabled: boolean;
      schedule: string;
      retention: number;
    };
    versioning?: {
      enabled: boolean;
      maxVersions: number;
      strategy: 'major' | 'minor' | 'patch' | 'custom';
    };
  };
  settings: {
    enabled: boolean;
    types: {
      string?: boolean;
      number?: boolean;
      boolean?: boolean;
      object?: boolean;
      array?: boolean;
      custom?: boolean;
    };
    validation?: {
      enabled: boolean;
      rules?: {
        name: string;
        type: string;
        required: boolean;
        pattern?: string;
        min?: number;
        max?: number;
        enum?: any[];
        custom?: Record<string, any>;
      }[];
    };
    inheritance?: {
      enabled: boolean;
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  monitoring: {
    enabled: boolean;
    metrics?: {
      settings?: boolean;
      usage?: boolean;
      performance?: boolean;
    };
    alerts?: {
      enabled: boolean;
      channels?: {
        type: 'email' | 'slack' | 'custom';
        config: Record<string, any>;
      }[];
    };
  };
  features: {
    settings?: boolean;
    monitoring?: boolean;
    audit?: boolean;
  };
  metadata: {
    title?: string;
    description?: string;
    version?: string;
    tags?: string[];
    custom?: Record<string, any>;
  };
}

export interface Configuration {
  id: string;
  config: ConfigurationConfig;
  type: ConfigurationType;
  status: ConfigurationStatus;
  scope: ConfigurationScope;
  name: string;
  description?: string;
  settings: {
    values: Record<string, any>;
    validation?: {
      rules: {
        name: string;
        type: string;
        required: boolean;
        pattern?: string;
        min?: number;
        max?: number;
        enum?: any[];
        custom?: Record<string, any>;
      }[];
    };
    inheritance?: {
      rules: {
        from: string;
        to: string;
        type: 'include' | 'exclude';
      }[];
    };
  };
  metadata?: {
    created?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    modified?: {
      date: Date;
      user: {
        id: string;
        name?: string;
        email?: string;
      };
    };
    version?: {
      number: string;
      type: 'major' | 'minor' | 'patch' | 'custom';
      changes?: string[];
    };
    tags?: string[];
    categories?: string[];
    custom?: Record<string, any>;
  };
  monitoring?: {
    metrics: {
      name: string;
      value: number;
      timestamp: Date;
    }[];
    alerts?: {
      name: string;
      condition: string;
      threshold: number;
      status: 'active' | 'triggered' | 'resolved';
      lastTriggered?: Date;
      lastResolved?: Date;
    }[];
  };
  stats?: {
    settings: number;
    usage: number;
    performance: {
      averageTime: number;
      maxTime: number;
    };
  };
  createdAt: Date;
  updatedAt: Date;
  deprecatedAt?: Date;
  deletedAt?: Date;
  expiresAt?: Date;
}

export interface ConfigurationLog {
  id: string;
  configuration: string;
  action: 'create' | 'update' | 'delete' | 'validate' | 'error';
  details?: Record<string, any>;
  timestamp: Date;
}

export interface ConfigurationStats {
  total: number;
  byType: Record<ConfigurationType, number>;
  byStatus: Record<ConfigurationStatus, number>;
  byScope: Record<ConfigurationScope, number>;
  settings: {
    total: number;
    byType?: Record<string, number>;
    byConfiguration?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  usage: {
    total: number;
    byType?: Record<string, number>;
    byConfiguration?: Record<string, number>;
    byDate?: Record<string, number>;
  };
  performance: {
    averageTime: number;
    maxTime: number;
    byConfiguration?: Record<string, number>;
    byDate?: Record<string, number>;
  };
}

export interface ConfigurationService {
  createConfig: (config: Omit<ConfigurationConfig, 'id'>) => ConfigurationConfig;
  updateConfig: (id: string, config: Partial<ConfigurationConfig>) => void;
  deleteConfig: (id: string) => void;
  getConfig: (id: string) => ConfigurationConfig | undefined;
  getConfigs: () => ConfigurationConfig[];
  create: (
    config: Omit<ConfigurationConfig, 'id'>,
    configuration: Omit<Configuration, 'id' | 'config'>
  ) => Promise<Configuration>;
  update: (id: string, configuration: Partial<Configuration>) => Promise<Configuration>;
  delete: (id: string) => Promise<void>;
  get: (id: string) => Configuration | undefined;
  getAll: (options?: {
    type?: ConfigurationType[];
    status?: ConfigurationStatus[];
    scope?: ConfigurationScope[];
    search?: string;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => Configuration[];
  search: (
    query: string,
    options?: {
      type?: ConfigurationType[];
      status?: ConfigurationStatus[];
      scope?: ConfigurationScope[];
      sort?: {
        field: string;
        direction: 'asc' | 'desc';
      };
      limit?: number;
      offset?: number;
    }
  ) => Configuration[];
  validate: (id: string, settings: Record<string, any>) => Promise<boolean>;
  getLogs: (options?: {
    configuration?: string;
    action?: ('create' | 'update' | 'delete' | 'validate' | 'error')[];
    startDate?: Date;
    endDate?: Date;
    sort?: {
      field: string;
      direction: 'asc' | 'desc';
    };
    limit?: number;
    offset?: number;
  }) => ConfigurationLog[];
  getStats: () => ConfigurationStats;
  clearStats: () => void;
  getMetadata: () => Record<string, any>;
  setMetadata: (metadata: Record<string, any>) => void;
  clearMetadata: () => void;
}
